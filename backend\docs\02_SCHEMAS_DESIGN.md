# 📋 SCHEMAS DESIGN - HƯỚNG DẪN CHI TIẾT

**Mục tiêu**: Hướng dẫn cách suy luận và thiết kế API schemas từ frontend requirements

---

## 🎯 **QUY TRÌNH THIẾT KẾ SCHEMAS**

### **Bước 1: API Requirements Analysis - Phân tích yêu cầu API**

#### **🤔 Câu hỏi cốt lõi:**
```
1. Frontend cần gửi data gì lên server? → Input Schemas
2. Frontend cần nhận data gì từ server? → Output Schemas
3. Validation rules gì cần thiết? → Field Constraints
4. Error responses như thế nào? → Error Schemas
5. Pagination cần không? → List Schemas
```

#### **Ví dụ thực tế: User Registration Flow**
```python
# Frontend Analysis:
"""
Registration Form có fields:
- Username input field
- Email input field  
- Password input field
- Confirm Password field (frontend only)
- Terms & Conditions checkbox

Success Response cần:
- User ID (để redirect)
- Username (để hiển thị welcome)
- Email (để show confirmation)
- Registration timestamp

Error Response cần:
- Field-specific errors
- General error message
- Error code for programmatic handling
"""

# Từ analysis → Schema design:
input_schema = "UserCreate"
output_schema = "UserResponse"  
error_schema = "ValidationErrorResponse"
```

### **Bước 2: Input Schema Design - Thiết kế schema đầu vào**

#### **🧠 Suy luận UserCreate schema:**
```python
class UserCreate(BaseModel):
    """
    Suy luận process:
    
    Q: User cần cung cấp gì để đăng ký?
    A: Username, email, password
    
    Q: Field nào required vs optional?
    A: Tất cả required cho registration
    
    Q: Validation rules gì cần thiết?
    A: Email format, username format, password strength
    
    Q: Có cần confirm_password field?
    A: Không - frontend validate, backend chỉ cần password
    """
    
    username: str = Field(
        ...,                    # Required
        min_length=2,          # Minimum meaningful length
        max_length=50,         # UI constraint
        regex=r'^[a-zA-Z0-9_-]+$',  # URL-safe characters
        description="Unique username for the account",
        example="john_doe"
    )
    
    email: EmailStr = Field(
        ...,                   # Required
        description="Valid email address for login and notifications",
        example="<EMAIL>"
    )
    
    password: str = Field(
        ...,                   # Required
        min_length=8,          # Security requirement
        max_length=128,        # Reasonable upper limit
        description="Password with at least 8 characters",
        example="SecurePass123!"
    )
    
    # Validation example
    @validator('username')
    def validate_username_content(cls, v):
        """Business rule: Username cannot be reserved words"""
        reserved = ['admin', 'api', 'www', 'mail']
        if v.lower() in reserved:
            raise ValueError('Username is reserved')
        return v
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Business rule: Password complexity requirements"""
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain number')
        return v
```

#### **🤔 Suy luận WoundImageUpload schema:**
```python
class WoundImageUpload(BaseModel):
    """
    Suy luận process:
    
    Q: Upload image cần metadata gì?
    A: Body part, wound type, description, privacy settings
    
    Q: File handling như thế nào?
    A: File upload qua multipart/form-data, metadata qua JSON
    
    Q: Optional vs required fields?
    A: Chỉ image required, metadata optional để UX tốt hơn
    """
    
    # Medical metadata
    body_part: Optional[str] = Field(
        None,
        max_length=100,
        description="Body part where wound is located",
        example="left_arm"
    )
    
    wound_type: Optional[WoundTypeEnum] = Field(
        None,
        description="Type of wound",
        example="cut"
    )
    
    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Additional description of the wound",
        example="Small cut from kitchen knife"
    )
    
    # Privacy settings
    is_private: bool = Field(
        default=True,          # Default to private for medical data
        description="Whether the image should be private"
    )
    
    # Consent
    consent_for_analysis: bool = Field(
        ...,                   # Required for legal compliance
        description="User consent for AI analysis"
    )

# Enum for controlled vocabulary
class WoundTypeEnum(str, Enum):
    """
    Suy luận: Medical domain cần controlled vocabulary
    → Standardize data, enable better analytics
    """
    CUT = "cut"
    BURN = "burn"
    BRUISE = "bruise"
    SCRAPE = "scrape"
    PUNCTURE = "puncture"
    OTHER = "other"
```

### **Bước 3: Output Schema Design - Thiết kế schema đầu ra**

#### **🧠 Suy luận UserResponse schema:**
```python
class UserResponse(BaseModel):
    """
    Suy luận process:
    
    Q: Frontend cần gì sau khi user đăng ký/đăng nhập?
    A: Basic user info + profile info để hiển thị
    
    Q: Sensitive data nào không được trả về?
    A: Password, internal flags (is_active, is_verified)
    
    Q: Profile data có luôn hay tách riêng?
    A: Include basic profile info để giảm API calls
    """
    
    # Core user data
    user_id: uuid.UUID = Field(
        description="Unique user identifier",
        example="123e4567-e89b-12d3-a456-************"
    )
    
    username: str = Field(
        description="User's display name",
        example="john_doe"
    )
    
    email: EmailStr = Field(
        description="User's email address",
        example="<EMAIL>"
    )
    
    created_at: datetime = Field(
        description="Account creation timestamp",
        example="2025-09-14T10:30:00Z"
    )
    
    # Profile data (optional - may not exist yet)
    full_name: Optional[str] = Field(
        None,
        description="User's full name from profile",
        example="John Doe"
    )
    
    phone: Optional[str] = Field(
        None,
        description="User's phone number",
        example="+**********"
    )
    
    avatar_url: Optional[str] = Field(
        None,
        description="URL to user's avatar image",
        example="https://cdn.example.com/avatars/user123.jpg"
    )
    
    # Computed fields
    @computed_field
    @property
    def display_name(self) -> str:
        """Return full name if available, otherwise username"""
        return self.full_name or self.username
    
    class Config:
        from_attributes = True  # Enable ORM conversion
```

#### **🤔 Suy luận WoundAnalysisResponse schema:**
```python
class WoundAnalysisResponse(BaseModel):
    """
    Suy luận process:
    
    Q: AI analysis results cần trả về gì?
    A: Wound classification, severity, confidence, recommendations
    
    Q: Medical data cần format như thế nào?
    A: Structured, với confidence scores, standardized terminology
    
    Q: Recommendations format?
    A: Prioritized list với actionable items
    """
    
    analysis_id: uuid.UUID = Field(
        description="Unique analysis identifier"
    )
    
    image_id: uuid.UUID = Field(
        description="ID of the analyzed image"
    )
    
    # AI Results
    wound_classification: WoundClassificationEnum = Field(
        description="AI-determined wound type"
    )
    
    severity_level: SeverityEnum = Field(
        description="Wound severity assessment"
    )
    
    confidence_score: float = Field(
        ge=0.0, le=1.0,
        description="AI confidence in the analysis (0-1)"
    )
    
    # Detailed analysis
    affected_area_percentage: Optional[float] = Field(
        None,
        ge=0.0, le=100.0,
        description="Percentage of image area affected"
    )
    
    healing_stage: Optional[HealingStageEnum] = Field(
        None,
        description="Current healing stage"
    )
    
    # Recommendations
    recommendations: List[RecommendationItem] = Field(
        description="Prioritized treatment recommendations"
    )
    
    # Metadata
    analyzed_at: datetime = Field(
        description="Analysis completion timestamp"
    )
    
    processing_time_ms: int = Field(
        description="AI processing time in milliseconds"
    )

class RecommendationItem(BaseModel):
    """Individual recommendation with priority and details"""
    
    priority: PriorityEnum = Field(
        description="Recommendation priority level"
    )
    
    category: RecommendationCategoryEnum = Field(
        description="Type of recommendation"
    )
    
    title: str = Field(
        max_length=200,
        description="Short recommendation title"
    )
    
    description: str = Field(
        max_length=1000,
        description="Detailed recommendation description"
    )
    
    urgency_level: UrgencyEnum = Field(
        description="How urgent this recommendation is"
    )
```

### **Bước 4: Error Schema Design - Thiết kế schema lỗi**

#### **🧠 Suy luận error response structure:**
```python
class ValidationErrorResponse(BaseModel):
    """
    Suy luận process:
    
    Q: Frontend cần gì khi validation fail?
    A: Field-specific errors để highlight UI elements
    
    Q: Error message format như thế nào?
    A: User-friendly messages, không technical
    
    Q: Multiple errors per field?
    A: Có - một field có thể vi phạm nhiều rules
    """
    
    success: bool = Field(
        default=False,
        description="Always false for error responses"
    )
    
    message: str = Field(
        description="General error message for user display",
        example="Please fix the following errors"
    )
    
    error_code: str = Field(
        description="Machine-readable error code",
        example="VALIDATION_ERROR"
    )
    
    field_errors: Dict[str, List[str]] = Field(
        description="Field-specific validation errors",
        example={
            "email": ["Invalid email format"],
            "password": [
                "Password too short",
                "Password must contain uppercase letter"
            ]
        }
    )
    
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Error occurrence timestamp"
    )

class BusinessErrorResponse(BaseModel):
    """
    Suy luận: Business logic errors khác validation errors
    → Cần different structure
    """
    
    success: bool = Field(default=False)
    
    message: str = Field(
        description="Business error message",
        example="Email already registered"
    )
    
    error_code: str = Field(
        description="Business error code",
        example="AUTH_002"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error context",
        example={"email": "<EMAIL>", "suggestion": "Try logging in instead"}
    )
    
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
```

### **Bước 5: List & Pagination Schema Design**

#### **🤔 Suy luận pagination requirements:**
```python
class PaginatedResponse(BaseModel, Generic[T]):
    """
    Suy luận process:
    
    Q: Large datasets cần pagination?
    A: Có - performance và UX
    
    Q: Pagination info nào cần thiết?
    A: Current page, total pages, total items, has_next/has_prev
    
    Q: Items per page configurable?
    A: Có - với reasonable limits
    """
    
    success: bool = Field(default=True)
    message: str = Field(default="Data retrieved successfully")
    
    # Paginated data
    data: List[T] = Field(
        description="List of items for current page"
    )
    
    # Pagination metadata
    pagination: PaginationMeta = Field(
        description="Pagination information"
    )
    
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class PaginationMeta(BaseModel):
    """Pagination metadata"""
    
    current_page: int = Field(
        ge=1,
        description="Current page number (1-based)"
    )
    
    per_page: int = Field(
        ge=1, le=100,          # Reasonable limits
        description="Items per page"
    )
    
    total_items: int = Field(
        ge=0,
        description="Total number of items"
    )
    
    total_pages: int = Field(
        ge=0,
        description="Total number of pages"
    )
    
    has_next: bool = Field(
        description="Whether there is a next page"
    )
    
    has_prev: bool = Field(
        description="Whether there is a previous page"
    )

# Usage example
class WoundImageListResponse(PaginatedResponse[WoundImageSummary]):
    """Paginated list of wound images"""
    pass

class WoundImageSummary(BaseModel):
    """Summary info for wound image list"""
    image_id: uuid.UUID
    thumbnail_url: str
    upload_date: datetime
    body_part: Optional[str]
    analysis_status: str
```

### **Bước 6: Schema Inheritance Strategy**

#### **🧠 Suy luận inheritance patterns:**
```python
# Base schemas for common patterns
class BaseResponse(BaseModel):
    """Common response fields"""
    success: bool
    message: str
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class TimestampMixin(BaseModel):
    """Common timestamp fields"""
    created_at: datetime
    updated_at: datetime

class UserBase(BaseModel):
    """Common user fields"""
    email: EmailStr
    username: str

# Inheritance examples
class UserCreate(UserBase):
    """Extend base for creation"""
    password: str = Field(min_length=8)

class UserUpdate(UserBase):
    """Extend base for updates - all optional"""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    password: Optional[str] = None

class UserResponse(UserBase, TimestampMixin):
    """Extend base with timestamps and additional fields"""
    user_id: uuid.UUID
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Start with frontend/API requirements analysis
- Use descriptive field names and examples
- Include proper validation with business rules
- Design consistent error response formats
- Use inheritance to avoid code duplication
- Add comprehensive field descriptions
- Consider pagination for list endpoints
- Use enums for controlled vocabularies

### **❌ DON'T:**
- Expose sensitive data in response schemas
- Use generic field names (data, info, etc.)
- Skip validation on input schemas
- Return different error formats across APIs
- Create overly complex nested structures
- Ignore field length limits
- Mix business logic in schema validation

### **🔍 REVIEW CHECKLIST:**
- [ ] All API endpoints have appropriate schemas?
- [ ] Input validation covers business rules?
- [ ] Error responses are user-friendly?
- [ ] Sensitive data excluded from responses?
- [ ] Pagination implemented for lists?
- [ ] Schema inheritance used effectively?
- [ ] Field descriptions and examples provided?

**Remember: Good schema design makes APIs self-documenting and developer-friendly!** 📋
