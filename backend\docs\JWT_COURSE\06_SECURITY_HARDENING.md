# 🛡️ LESSON 06: SECURITY HARDENING - TĂNG CƯỜNG BẢO MẬT

**<PERSON><PERSON><PERSON> tiêu**: Implement advanced security measures để protect JWT system khỏi common attacks

---

## 🎯 **PHẦN 1: ACCOUNT SECURITY - BẢO MẬT TÀI KHOẢN**

### **🔒 Account Lockout Protection**

```python
# auth/security.py - Enhanced với account lockout
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

class AccountSecurity:
    """Account security management"""
    
    def __init__(self):
        self.max_failed_attempts = 5
        self.lockout_duration_minutes = 30
        self.failed_attempts = {}  # user_id -> attempt count
        self.lockout_times = {}    # user_id -> lockout expiry
    """
    Gi<PERSON>i thích Account Security Settings:
    - max_failed_attempts: 5 failed attempts trigger lockout
    - lockout_duration_minutes: 30 minutes lockout period
    - failed_attempts: Track attempts per user
    - lockout_times: Track lockout expiry times
    """
    
    def is_account_locked(self, user_id: int) -> bool:
        """
        Check if account is currently locked
        
        Args:
            user_id: User ID to check
        
        Returns:
            bool: True nếu account locked
        """
        if user_id not in self.lockout_times:
            return False
        
        lockout_expiry = self.lockout_times[user_id]
        if datetime.utcnow() > lockout_expiry:
            # Lockout expired, clean up
            del self.lockout_times[user_id]
            if user_id in self.failed_attempts:
                del self.failed_attempts[user_id]
            return False
        
        return True
        """
        Giải thích Lockout Check:
        1. Check if user có lockout time
        2. Compare với current time
        3. Clean up expired lockouts
        4. Return current lockout status
        """
    
    def record_failed_attempt(self, user_id: int) -> bool:
        """
        Record failed login attempt
        
        Args:
            user_id: User ID
        
        Returns:
            bool: True nếu account should be locked
        """
        # Increment failed attempt count
        current_attempts = self.failed_attempts.get(user_id, 0) + 1
        self.failed_attempts[user_id] = current_attempts
        
        # Check if should lock account
        if current_attempts >= self.max_failed_attempts:
            lockout_expiry = datetime.utcnow() + timedelta(minutes=self.lockout_duration_minutes)
            self.lockout_times[user_id] = lockout_expiry
            return True
        
        return False
        """
        Giải thích Failed Attempt Logic:
        1. Increment attempt counter
        2. Check if reached max attempts
        3. Set lockout expiry time
        4. Return lockout status
        """
    
    def record_successful_login(self, user_id: int):
        """
        Record successful login (reset failed attempts)
        
        Args:
            user_id: User ID
        """
        if user_id in self.failed_attempts:
            del self.failed_attempts[user_id]
        if user_id in self.lockout_times:
            del self.lockout_times[user_id]
        """
        Giải thích Success Reset:
        - Clear failed attempt counter
        - Remove any lockout times
        - Fresh start after successful login
        """

# Global instance
account_security = AccountSecurity()
```

### **🕒 Rate Limiting**

```python
# auth/rate_limiter.py - Rate limiting implementation
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Deque

class RateLimiter:
    """Rate limiting cho authentication endpoints"""
    
    def __init__(self):
        # Track requests per IP address
        self.requests: Dict[str, Deque[datetime]] = defaultdict(deque)
        self.login_limit = 5      # 5 login attempts
        self.login_window = 300   # per 5 minutes
        self.api_limit = 100      # 100 API requests  
        self.api_window = 60      # per minute
    """
    Giải thích Rate Limiting Settings:
    - requests: Track request timestamps per IP
    - login_limit: Max login attempts trong window
    - login_window: Time window in seconds
    - api_limit: Max API requests trong window
    - Separate limits cho different endpoint types
    """
    
    def is_rate_limited(self, ip_address: str, endpoint_type: str = "api") -> bool:
        """
        Check if IP is rate limited
        
        Args:
            ip_address: Client IP address
            endpoint_type: "login" hoặc "api"
        
        Returns:
            bool: True nếu rate limited
        """
        now = datetime.utcnow()
        
        # Get limits cho endpoint type
        if endpoint_type == "login":
            limit = self.login_limit
            window = self.login_window
        else:
            limit = self.api_limit
            window = self.api_window
        
        # Clean old requests outside window
        cutoff_time = now - timedelta(seconds=window)
        request_times = self.requests[ip_address]
        
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        # Check if over limit
        return len(request_times) >= limit
        """
        Giải thích Rate Limit Check:
        1. Get current time
        2. Determine limits cho endpoint type
        3. Remove old requests outside window
        4. Check if current requests >= limit
        """
    
    def record_request(self, ip_address: str):
        """
        Record new request từ IP
        
        Args:
            ip_address: Client IP address
        """
        self.requests[ip_address].append(datetime.utcnow())
        """
        Giải thích Request Recording:
        - Add current timestamp to IP's request list
        - Used after rate limit check passes
        - Maintains sliding window of requests
        """

# Global instance
rate_limiter = RateLimiter()
```

## 🔐 **PHẦN 2: TOKEN SECURITY - BẢO MẬT TOKEN**

### **🚫 Token Blacklisting**

```python
# auth/token_blacklist.py - Token blacklisting system
from datetime import datetime, timedelta
from typing import Set, Dict
import hashlib

class TokenBlacklist:
    """Token blacklisting để revoke tokens before expiry"""
    
    def __init__(self):
        self.blacklisted_tokens: Set[str] = set()
        self.blacklist_expiry: Dict[str, datetime] = {}
    """
    Giải thích Blacklist Storage:
    - blacklisted_tokens: Set of revoked token hashes
    - blacklist_expiry: When to remove từ blacklist
    - Memory-based cho tutorial, production dùng Redis
    """
    
    def _hash_token(self, token: str) -> str:
        """
        Hash token để store trong blacklist
        
        Args:
            token: JWT token string
        
        Returns:
            str: SHA-256 hash of token
        """
        return hashlib.sha256(token.encode()).hexdigest()
        """
        Giải thích Token Hashing:
        - Store hash thay vì full token
        - Reduces memory usage
        - Adds security layer
        - SHA-256 provides good distribution
        """
    
    def blacklist_token(self, token: str, expires_at: datetime):
        """
        Add token to blacklist
        
        Args:
            token: Token to blacklist
            expires_at: When token would naturally expire
        """
        token_hash = self._hash_token(token)
        self.blacklisted_tokens.add(token_hash)
        self.blacklist_expiry[token_hash] = expires_at
        """
        Giải thích Blacklist Addition:
        - Hash token before storing
        - Set expiry to token's natural expiry
        - No need to keep after token expires naturally
        """
    
    def is_token_blacklisted(self, token: str) -> bool:
        """
        Check if token is blacklisted
        
        Args:
            token: Token to check
        
        Returns:
            bool: True nếu token blacklisted
        """
        # Clean expired entries first
        self._cleanup_expired()
        
        token_hash = self._hash_token(token)
        return token_hash in self.blacklisted_tokens
        """
        Giải thích Blacklist Check:
        1. Clean expired entries
        2. Hash incoming token
        3. Check if hash trong blacklist
        4. Return boolean result
        """
    
    def _cleanup_expired(self):
        """Clean up expired blacklist entries"""
        now = datetime.utcnow()
        expired_hashes = [
            token_hash for token_hash, expiry in self.blacklist_expiry.items()
            if expiry < now
        ]
        
        for token_hash in expired_hashes:
            self.blacklisted_tokens.discard(token_hash)
            del self.blacklist_expiry[token_hash]
        """
        Giải thích Cleanup Process:
        1. Find expired entries
        2. Remove từ blacklist set
        3. Remove từ expiry dict
        4. Prevents memory leaks
        """

# Global instance
token_blacklist = TokenBlacklist()
```

### **🔍 Device Fingerprinting**

```python
# auth/device_fingerprint.py - Device fingerprinting
import hashlib
from typing import Optional
from fastapi import Request

class DeviceFingerprinter:
    """Generate device fingerprints cho security"""
    
    def generate_fingerprint(self, request: Request) -> str:
        """
        Generate device fingerprint từ request
        
        Args:
            request: FastAPI request object
        
        Returns:
            str: Device fingerprint hash
        """
        # Collect fingerprint components
        user_agent = request.headers.get("user-agent", "")
        accept_language = request.headers.get("accept-language", "")
        accept_encoding = request.headers.get("accept-encoding", "")
        
        # Get client IP (handle proxies)
        client_ip = self._get_client_ip(request)
        
        # Create fingerprint string
        fingerprint_data = f"{user_agent}|{accept_language}|{accept_encoding}|{client_ip}"
        
        # Hash fingerprint
        fingerprint_hash = hashlib.sha256(fingerprint_data.encode()).hexdigest()
        return fingerprint_hash[:16]  # First 16 chars
        """
        Giải thích Fingerprint Generation:
        1. Collect stable browser/device characteristics
        2. Combine into single string
        3. Hash để create consistent identifier
        4. Truncate để reduce storage
        """
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get real client IP (handle proxies)
        
        Args:
            request: FastAPI request object
        
        Returns:
            str: Client IP address
        """
        # Check proxy headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # Take first IP trong chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host
        """
        Giải thích IP Detection:
        - X-Forwarded-For: Proxy chain, take first
        - X-Real-IP: Single proxy setup
        - request.client.host: Direct connection
        - Handle common proxy configurations
        """
    
    def verify_fingerprint(self, request: Request, stored_fingerprint: str) -> bool:
        """
        Verify request matches stored fingerprint
        
        Args:
            request: Current request
            stored_fingerprint: Previously stored fingerprint
        
        Returns:
            bool: True nếu fingerprints match
        """
        current_fingerprint = self.generate_fingerprint(request)
        return current_fingerprint == stored_fingerprint
        """
        Giải thích Fingerprint Verification:
        - Generate fingerprint từ current request
        - Compare với stored fingerprint
        - Exact match required
        - Used để detect token theft
        """

# Global instance
device_fingerprinter = DeviceFingerprinter()
```

## 🔐 **PHẦN 3: ENHANCED AUTHENTICATION**

### **🛡️ Secure Login Implementation**

```python
# auth/routes.py - Enhanced login với security measures
from fastapi import Request, HTTPException, status
from .rate_limiter import rate_limiter
from .device_fingerprint import device_fingerprinter
from .token_blacklist import token_blacklist

@router.post("/login", response_model=TokenPair)
async def secure_login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    Secure login với rate limiting, account lockout, device fingerprinting
    """
    client_ip = device_fingerprinter._get_client_ip(request)
    
    # Check rate limiting
    if rate_limiter.is_rate_limited(client_ip, "login"):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many login attempts. Please try again later."
        )
    """
    Giải thích Rate Limiting Check:
    - Check before any processing
    - Return 429 Too Many Requests
    - Prevents brute force attacks
    - Per-IP rate limiting
    """
    
    # Get user
    user_dict = get_user(form_data.username)
    if not user_dict:
        # Record request even for non-existent users
        rate_limiter.record_request(client_ip)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    
    # Check account lockout
    if account_security.is_account_locked(user_dict["id"]):
        rate_limiter.record_request(client_ip)
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account temporarily locked due to too many failed attempts"
        )
    """
    Giải thích Account Lockout Check:
    - Check after user exists
    - Return 423 Locked status
    - Still record rate limit attempt
    - Prevents continued attacks on locked accounts
    """
    
    # Verify password
    if not verify_password(form_data.password, user_dict["hashed_password"]):
        # Record failed attempt
        account_security.record_failed_attempt(user_dict["id"])
        rate_limiter.record_request(client_ip)
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    
    # Successful login
    account_security.record_successful_login(user_dict["id"])
    rate_limiter.record_request(client_ip)
    
    # Generate device fingerprint
    device_fingerprint = device_fingerprinter.generate_fingerprint(request)
    
    # Create token pair với device info
    token_pair = create_token_pair(user_dict, device_fingerprint)
    
    return token_pair
    """
    Giải thích Successful Login:
    1. Reset failed attempts
    2. Record rate limit request
    3. Generate device fingerprint
    4. Create tokens với device info
    5. Return token pair
    """

@router.post("/logout")
async def logout(
    request: Request,
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Secure logout với token blacklisting
    """
    # Get token từ request
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Decode token để get expiry
        payload = verify_token(token)
        if payload:
            expires_at = datetime.fromtimestamp(payload["exp"])
            token_blacklist.blacklist_token(token, expires_at)
    
    # Revoke refresh tokens
    revoke_user_tokens(current_user.id)
    
    return {"message": "Successfully logged out"}
    """
    Giải thích Secure Logout:
    1. Extract access token từ header
    2. Add to blacklist với expiry time
    3. Revoke all refresh tokens
    4. Complete session termination
    """
```

## 🎯 **LESSON 06 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Security Features Implemented:**
- **Account Lockout**: Protection against brute force attacks
- **Rate Limiting**: Per-IP request throttling
- **Token Blacklisting**: Immediate token revocation capability
- **Device Fingerprinting**: Detect token theft across devices
- **Secure Logout**: Complete session termination

### **🧠 Critical Security Understanding:**
- **Defense in Depth**: Multiple security layers
- **Rate Limiting Strategy**: Different limits cho different endpoints
- **Token Lifecycle**: Creation, usage, revocation, expiry
- **Device Binding**: Tie tokens to specific devices

### **🔧 Production Considerations:**
- **Distributed Storage**: Redis cho rate limiting và blacklisting
- **Monitoring**: Track security events và patterns
- **Alerting**: Notify on suspicious activities
- **Performance**: Optimize security checks

### **🎯 Next Steps:**
- **Lesson 07**: Production Security Patterns
- **Practice**: Test security measures thoroughly
- **Monitoring**: Add security event logging

**Bạn đã có hardened JWT system! Ready for production security?** 🚀
