# 🔐 LESSON 01: JWT FUNDAMENTALS - KIẾN THỨC NỀN TẢNG

**<PERSON><PERSON><PERSON> tiêu**: Hi<PERSON><PERSON> bản chất JWT, tại sao cần thiết, và cách hoạt động cơ bản

---

## 🎯 **PHẦN 1: AUTHENTICATION PROBLEM - VẤN ĐỀ XÁC THỰC**

### **🤔 Tại sao cần authentication?**

```python
# Scenario thực tế:
"""
Bạn vào Facebook:
1. Nhập email + password → Login thành công
2. Click vào profile → Facebook biết bạn là ai?
3. Post status → Facebook biết ai đang post?
4. Refresh page → Vẫn đăng nhập?

Câu hỏi: Làm sao server nhớ bạn đã login?
"""
# Giải thích: Đ<PERSON>y là vấn đề cốt lõi của web authentication.
# HTTP protocol không có khái niệm "session" hay "state".
# Mỗi request HTTP hoàn toàn độc lập với request trước đó.

# HTTP là stateless protocol
"""
HTTP Request 1: POST /login {email, password}
HTTP Response 1: 200 OK "Login successful"

HTTP Request 2: GET /profile
Server: "Ai đang request? Tôi không biết!"
"""
# Giải thích: Server không thể nhớ rằng request 2 đến từ
# cùng user đã login thành công ở request 1.
```

**Giải thích chi tiết các khái niệm:**
- **HTTP stateless**: Mỗi request độc lập, server không nhớ request trước. Đây là design principle của HTTP để đảm bảo scalability.
- **Authentication**: Xác minh danh tính (bạn là ai?). Process này verify credentials như username/password.
- **Authorization**: Xác minh quyền hạn (bạn được làm gì?). Process này check permissions sau khi đã authenticate.
- **Session management**: Duy trì trạng thái đăng nhập giữa các requests. Đây chính là challenge chúng ta cần giải quyết.

### **🏛️ Traditional Solutions - Giải pháp truyền thống**

#### **Solution 1: Session-based Authentication**

```python
# Server-side session storage
"""
1. User login → Server tạo session
2. Server lưu session vào memory/database
3. Server trả về session_id cho client
4. Client gửi session_id với mỗi request
5. Server lookup session để biết user
"""

# Example session storage
sessions = {
    "abc123": {
        "user_id": "user_456",
        "username": "john_doe",
        "login_time": "2023-12-01 10:00:00",
        "expires": "2023-12-01 22:00:00"
    }
}

# Client request
"""
GET /profile
Cookie: session_id=abc123

Server checks: sessions["abc123"] → user_456
"""
```

**Ưu điểm Session-based:**
- **Server control**: Có thể revoke session bất kỳ lúc nào
- **Security**: Session data không expose cho client
- **Flexibility**: Có thể lưu complex data trong session

**Nhược điểm Session-based:**
- **Memory usage**: Server phải lưu trữ tất cả sessions
- **Scaling issues**: Multiple servers cần shared session storage
- **Mobile unfriendly**: Mobile apps khó handle cookies
- **CORS complexity**: Cross-domain cookie issues

#### **Solution 2: Cookie-based Authentication**

```python
# Browser tự động gửi cookies
"""
1. User login → Server set cookie
2. Browser tự động attach cookie vào mỗi request
3. Server đọc cookie để identify user
"""

# Server response after login
"""
HTTP/1.1 200 OK
Set-Cookie: auth_token=xyz789; HttpOnly; Secure; SameSite=Strict
"""

# Subsequent requests
"""
GET /profile
Cookie: auth_token=xyz789
"""
```

**Ưu điểm Cookies:**
- **Automatic**: Browser tự động handle
- **Secure options**: HttpOnly, Secure, SameSite flags
- **Domain scoped**: Chỉ gửi cho specific domains

**Nhược điểm Cookies:**
- **CSRF vulnerable**: Cross-site request forgery attacks
- **Domain limitations**: Không work cross-domain tốt
- **Mobile issues**: Native apps không có cookie engine
- **Size limits**: 4KB limit per cookie

## 🚀 **PHẦN 2: JWT SOLUTION - GIẢI PHÁP JWT**

### **🧠 JWT Concept - Khái niệm JWT**

```python
# JWT = JSON Web Token
"""
JWT là một self-contained token:
- Chứa thông tin user ngay trong token
- Không cần server lưu trữ session
- Stateless authentication
- Works với web, mobile, APIs
"""

# JWT workflow
"""
1. User login → Server tạo JWT chứa user info
2. Server trả JWT cho client
3. Client lưu JWT (localStorage/memory)
4. Client gửi JWT với mỗi request
5. Server verify JWT → extract user info
"""
```

**Tại sao JWT giải quyết được problems:**
- **Stateless**: Server không cần lưu session
- **Scalable**: Multiple servers không cần shared storage
- **Mobile-friendly**: Dễ implement trong mobile apps
- **Cross-domain**: Không có cookie domain restrictions

### **🔍 JWT Structure - Cấu trúc JWT**

```python
# JWT Format: Header.Payload.Signature
"""
Example JWT:
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

Phần 1: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9 (Header)
Phần 2: eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ (Payload)
Phần 3: SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c (Signature)
"""
```

#### **Part 1: Header - Phần đầu**

```python
# Header chứa metadata về token
header = {
    "alg": "HS256",    # Algorithm used for signing
    "typ": "JWT"       # Token type
}

# Base64URL encode
import base64
import json

header_json = json.dumps(header)
header_encoded = base64.urlsafe_b64encode(header_json.encode()).decode().rstrip('=')
print(header_encoded)  # eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
```

**Giải thích Header:**
- **alg**: Thuật toán mã hóa (HS256 = HMAC SHA-256)
- **typ**: Loại token (luôn là "JWT")
- **Base64URL encoding**: Encode để token có thể truyền qua URL

#### **Part 2: Payload - Phần dữ liệu**

```python
# Payload chứa claims (thông tin về user)
payload = {
    # Standard claims (RFC 7519)
    "sub": "1234567890",        # Subject (user ID)
    "name": "John Doe",         # User name
    "iat": 1516239022,          # Issued at (timestamp)
    "exp": 1516242622,          # Expires at (timestamp)
    
    # Custom claims (application-specific)
    "role": "admin",
    "permissions": ["read", "write"]
}

# Base64URL encode
payload_json = json.dumps(payload)
payload_encoded = base64.urlsafe_b64encode(payload_json.encode()).decode().rstrip('=')
```

**Giải thích Payload:**
- **Standard claims**: Định nghĩa trong JWT spec
  - `sub`: Subject (thường là user ID)
  - `iat`: Issued at (thời gian tạo token)
  - `exp`: Expiration (thời gian hết hạn)
  - `iss`: Issuer (ai tạo token)
  - `aud`: Audience (token dành cho ai)
- **Custom claims**: Thông tin specific cho application
- **Security note**: Payload không encrypted, chỉ encoded!

#### **Part 3: Signature - Chữ ký**

```python
# Signature đảm bảo token không bị tamper
import hmac
import hashlib

# Create signature
secret_key = "your-256-bit-secret"
message = header_encoded + "." + payload_encoded

# HMAC SHA-256
signature = hmac.new(
    secret_key.encode(),
    message.encode(),
    hashlib.sha256
).digest()

# Base64URL encode
signature_encoded = base64.urlsafe_b64encode(signature).decode().rstrip('=')

# Final JWT
jwt_token = f"{header_encoded}.{payload_encoded}.{signature_encoded}"
```

**Giải thích Signature:**
- **Purpose**: Đảm bảo token không bị modify
- **HMAC**: Hash-based Message Authentication Code
- **Secret key**: Chỉ server biết, dùng để sign và verify
- **Tamper detection**: Nếu payload thay đổi, signature sẽ invalid

## 🔄 **PHẦN 3: JWT WORKFLOW - LUỒNG HOẠT ĐỘNG**

### **🎯 Authentication Flow - Luồng xác thực**

```python
# Step 1: User Login
"""
Client → Server: POST /login
{
    "email": "<EMAIL>",
    "password": "secret123"
}
"""

# Step 2: Server Verification
"""
Server:
1. Tìm user trong database
2. Verify password với hashed password
3. Nếu OK → tạo JWT token
"""

def authenticate_user(email: str, password: str):
    # Tìm user trong database
    user = database.find_user_by_email(email)
    if not user:
        return None
    
    # Verify password
    if not verify_password(password, user.hashed_password):
        return None
    
    return user

# Step 3: JWT Creation
def create_jwt_token(user):
    payload = {
        "sub": str(user.id),           # User ID
        "email": user.email,           # User email
        "role": user.role,             # User role
        "iat": int(time.time()),       # Issued at
        "exp": int(time.time()) + 3600 # Expires in 1 hour
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")
    return token

# Step 4: Server Response
"""
Server → Client: 200 OK
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600
}
"""
```

### **🔐 Protected Request Flow - Luồng request được bảo vệ**

```python
# Step 1: Client Request với JWT
"""
Client → Server: GET /profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
"""

# Step 2: Server JWT Verification
def verify_jwt_token(token: str):
    try:
        # Decode và verify signature
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        
        # Check expiration
        if payload["exp"] < time.time():
            return None  # Token expired
        
        return payload
    except jwt.InvalidTokenError:
        return None  # Invalid token

# Step 3: Extract User Context
def get_current_user(token: str):
    payload = verify_jwt_token(token)
    if not payload:
        raise HTTPException(401, "Invalid token")
    
    user_id = payload["sub"]
    user = database.find_user_by_id(user_id)
    return user

# Step 4: Process Request với User Context
"""
Server có user context → process request → return response
"""
```

**Giải thích Workflow:**
- **Stateless**: Server không lưu token, chỉ verify
- **Self-contained**: Token chứa đủ info để identify user
- **Scalable**: Multiple servers có thể verify cùng token
- **Performance**: Không cần database lookup để verify token

## 🎯 **PHẦN 4: SECURITY CONSIDERATIONS - CÂN NHẮC BẢO MẬT**

### **🛡️ JWT Security Principles**

```python
# Security Rule 1: Secret Key Management
"""
❌ BAD: Weak secret
SECRET_KEY = "123456"

✅ GOOD: Strong secret
SECRET_KEY = "a-very-long-random-256-bit-secret-key-that-nobody-can-guess"

✅ BETTER: Environment variable
SECRET_KEY = os.getenv("JWT_SECRET_KEY")
"""

# Security Rule 2: Token Expiration
"""
❌ BAD: Long-lived tokens
"exp": int(time.time()) + 86400 * 365  # 1 year

✅ GOOD: Short-lived tokens
"exp": int(time.time()) + 3600  # 1 hour
"""

# Security Rule 3: Sensitive Data
"""
❌ BAD: Sensitive data in payload
{
    "sub": "123",
    "password": "secret123",      # Never!
    "credit_card": "1234-5678"    # Never!
}

✅ GOOD: Minimal claims
{
    "sub": "123",
    "role": "user",
    "exp": 1234567890
}
"""
```

### **🚨 Common JWT Vulnerabilities**

```python
# Vulnerability 1: None Algorithm Attack
"""
Attacker modifies header:
{
    "alg": "none",  # No signature required!
    "typ": "JWT"
}

Prevention: Always validate algorithm
"""
def verify_token_secure(token: str):
    # Specify allowed algorithms
    payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
    return payload

# Vulnerability 2: Algorithm Confusion
"""
Server uses RS256 (public key) for verification
Attacker uses HS256 (symmetric key) with public key as secret

Prevention: Strict algorithm validation
"""

# Vulnerability 3: Token Storage
"""
❌ BAD: localStorage (XSS vulnerable)
localStorage.setItem("token", jwt_token)

✅ BETTER: Memory storage
let token = jwt_token  // Lost on page refresh

✅ BEST: httpOnly cookie (for refresh tokens)
Set-Cookie: refresh_token=...; HttpOnly; Secure; SameSite=Strict
"""
```

## 📝 **LESSON 01 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Concepts Learned:**
- **Authentication Problem**: HTTP stateless, cần cách identify user
- **Traditional Solutions**: Sessions và cookies với limitations
- **JWT Solution**: Self-contained, stateless tokens
- **JWT Structure**: Header.Payload.Signature format
- **JWT Workflow**: Login → Token → Verify → Access
- **Security Basics**: Secret management, expiration, vulnerabilities

### **🧠 Critical Understanding:**
- **JWT không phải magic bullet**: Có trade-offs
- **Stateless có giá**: Khó revoke tokens
- **Security first**: Secret key và expiration quan trọng
- **Payload không encrypted**: Chỉ encoded, ai cũng đọc được

### **🎯 Next Steps:**
- **Lesson 02**: Implement JWT trong FastAPI
- **Practice**: Tạo JWT manually để hiểu sâu
- **Experiment**: Decode JWT tokens trên jwt.io

**Bạn đã hiểu foundation của JWT! Ready for implementation?** 🚀
