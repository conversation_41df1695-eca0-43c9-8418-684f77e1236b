# 🛣️ ROUTES DESIGN - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn cách suy luận và thiết kế API routes từ RESTful principles

---

## 🎯 **QUY TRÌNH THIẾT KẾ ROUTES**

### **Bước 1: RESTful Design Analysis - Phân tích thiết kế RESTful**

#### **🤔 Câu hỏi cốt lõi:**
```
1. Resources nào cần expose qua API?
2. Operations nào cần support cho mỗi resource?
3. URL structure như thế nào cho intuitive?
4. HTTP methods nào phù hợp cho từng operation?
5. Nested resources hay flat structure?
```

#### **Ví dụ thực tế: SkinAid API Resources**
```python
# Resource Analysis:
"""
Core Resources:
1. Users - User account management
2. Profiles - User profile information
3. WoundImages - Uploaded wound images
4. Analyses - AI analysis results
5. Recommendations - Treatment recommendations

Resource Relationships:
- User 1:1 Profile
- User 1:N WoundImages
- WoundImage 1:1 Analysis
- Analysis 1:N Recommendations
"""

# RESTful URL Design:
api_structure = {
    "/users": "User collection operations",
    "/users/{id}": "Individual user operations",
    "/users/{id}/profile": "User's profile (nested resource)",
    "/users/{id}/wound-images": "User's wound images collection",
    "/wound-images": "All wound images (admin only)",
    "/wound-images/{id}": "Individual wound image",
    "/wound-images/{id}/analysis": "Image analysis result",
    "/analyses/{id}/recommendations": "Analysis recommendations"
}
```

### **Bước 2: HTTP Method Selection - Lựa chọn phương thức HTTP**

#### **🧠 Suy luận HTTP method mapping:**
```python
# HTTP Methods Semantic Analysis:
"""
GET - Retrieve data (safe, idempotent)
POST - Create new resource (not idempotent)
PUT - Update entire resource (idempotent)
PATCH - Partial update (not necessarily idempotent)
DELETE - Remove resource (idempotent)
"""

# Route Design Examples:
class AuthRoutes:
    """
    Suy luận: Authentication routes
    - POST for login/register (creates session/user)
    - DELETE for logout (destroys session)
    - GET for current user info
    """
    
    # POST /auth/register - Create new user account
    # Why POST? Creating new resource (user)
    # Not idempotent: multiple calls create multiple users (if allowed)
    
    # POST /auth/login - Create new session/token
    # Why POST? Creating authentication session
    # Not idempotent: each call creates new token
    
    # DELETE /auth/logout - Destroy current session
    # Why DELETE? Removing authentication session
    # Idempotent: multiple calls have same effect
    
    # GET /auth/me - Get current user information
    # Why GET? Retrieving data, no side effects
    # Safe and idempotent

class UserRoutes:
    """
    Suy luận: User resource routes
    - Standard CRUD operations
    - RESTful URL patterns
    """
    
    # GET /users - List users (with pagination/filtering)
    # Why GET? Retrieving collection data
    
    # POST /users - Create new user (admin operation)
    # Why POST? Creating new resource
    
    # GET /users/{id} - Get specific user
    # Why GET? Retrieving individual resource
    
    # PUT /users/{id} - Update entire user record
    # Why PUT? Replacing entire resource
    # Idempotent: same request produces same result
    
    # PATCH /users/{id} - Partial user update
    # Why PATCH? Updating specific fields only
    
    # DELETE /users/{id} - Delete user account
    # Why DELETE? Removing resource
```

### **Bước 3: URL Structure Design - Thiết kế cấu trúc URL**

#### **🤔 Suy luận URL naming conventions:**
```python
# URL Design Principles:
"""
1. Use nouns, not verbs (resources, not actions)
2. Use plural nouns for collections
3. Use kebab-case for multi-word resources
4. Keep URLs predictable and intuitive
5. Use nested resources sparingly
"""

# ✅ Good URL Examples:
good_urls = [
    "GET /api/v1/users",                    # Collection
    "GET /api/v1/users/123",               # Individual resource
    "GET /api/v1/users/123/profile",       # Nested resource (1:1)
    "GET /api/v1/wound-images",            # Kebab-case for multi-word
    "POST /api/v1/wound-images/123/analyze", # Action on resource
    "GET /api/v1/analyses/456/recommendations" # Nested collection
]

# ❌ Bad URL Examples:
bad_urls = [
    "GET /api/v1/getUsers",                # Verb in URL
    "GET /api/v1/user",                    # Singular for collection
    "GET /api/v1/wound_images",            # Snake_case (inconsistent)
    "POST /api/v1/createUser",             # Action in URL
    "GET /api/v1/users/123/profile/details/personal" # Too deeply nested
]

# FastAPI Route Implementation:
from fastapi import APIRouter

auth_router = APIRouter(prefix="/auth", tags=["Authentication"])
user_router = APIRouter(prefix="/users", tags=["Users"])
profile_router = APIRouter(prefix="/profile", tags=["Profiles"])
wound_router = APIRouter(prefix="/wound-images", tags=["Wound Images"])
```

### **Bước 4: Route Parameter Design - Thiết kế tham số route**

#### **🧠 Suy luận parameter patterns:**
```python
from fastapi import APIRouter, Path, Query, Depends
from typing import Optional
import uuid

router = APIRouter()

@router.get("/users/{user_id}")
async def get_user(
    user_id: uuid.UUID = Path(
        ..., 
        description="Unique identifier for the user",
        example="123e4567-e89b-12d3-a456-************"
    )
):
    """
    Suy luận path parameters:
    - Use UUID for security (no enumeration attacks)
    - Descriptive parameter names
    - Include examples for documentation
    - Use Path() for validation and documentation
    """
    pass

@router.get("/users")
async def list_users(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, min_length=2, description="Search term"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    sort_by: str = Query("created_at", regex="^(created_at|username|email)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$")
):
    """
    Suy luận query parameters:
    - Pagination: page, per_page with reasonable limits
    - Filtering: Optional filters for common use cases
    - Searching: Optional search with minimum length
    - Sorting: Controlled sort fields and order
    - Validation: Use Query() with constraints
    """
    pass

@router.post("/wound-images/{image_id}/analyze")
async def analyze_wound_image(
    image_id: uuid.UUID = Path(..., description="Wound image identifier"),
    analysis_options: AnalysisOptions = Body(...),
    current_user: User = Depends(get_current_user)
):
    """
    Suy luận complex parameters:
    - Path parameter: Resource identifier
    - Body parameter: Complex analysis options
    - Dependency: Authentication/authorization
    """
    pass
```

### **Bước 5: Response Design - Thiết kế phản hồi**

#### **🤔 Suy luận response patterns:**
```python
from fastapi import status
from typing import List

@router.post("/users", status_code=status.HTTP_201_CREATED)
async def create_user(user_data: UserCreate) -> SuccessResponse[UserResponse]:
    """
    Suy luận POST response:
    - 201 Created status (resource created)
    - Return created resource data
    - Include location header (optional)
    """
    pass

@router.get("/users/{user_id}")
async def get_user(user_id: uuid.UUID) -> SuccessResponse[UserResponse]:
    """
    Suy luận GET response:
    - 200 OK status (default)
    - Return resource data
    - 404 if not found (handled by exception)
    """
    pass

@router.get("/users")
async def list_users(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100)
) -> SuccessResponse[List[UserResponse]]:
    """
    Suy luận collection response:
    - Return list of resources
    - Include pagination metadata
    - Consider using PaginatedResponse wrapper
    """
    pass

@router.put("/users/{user_id}")
async def update_user(
    user_id: uuid.UUID,
    user_data: UserUpdate
) -> SuccessResponse[UserResponse]:
    """
    Suy luận PUT response:
    - 200 OK with updated resource
    - Or 204 No Content if no response body needed
    """
    pass

@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: uuid.UUID):
    """
    Suy luận DELETE response:
    - 204 No Content (successful deletion)
    - No response body needed
    - 404 if resource doesn't exist
    """
    pass
```

### **Bước 6: Route Organization - Tổ chức routes**

#### **🧠 Suy luận modular route structure:**
```python
# File structure organization:
"""
backend/app/api/v1/
├── __init__.py
├── api.py              # Main API router
├── auth/
│   ├── __init__.py
│   └── routes.py       # Authentication routes
├── users/
│   ├── __init__.py
│   └── routes.py       # User management routes
├── profiles/
│   ├── __init__.py
│   └── routes.py       # Profile management routes
└── wounds/
    ├── __init__.py
    ├── images.py       # Wound image routes
    └── analyses.py     # Analysis routes
"""

# auth/routes.py
from fastapi import APIRouter, Depends, HTTPException, status
from ...modules.auth.controllers.auth_controller import AuthController
from ...core.database import get_db

router = APIRouter()

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse[UserResponse]:
    """
    Register new user account
    
    Suy luận route design:
    - Clear endpoint name
    - Appropriate status code
    - Dependency injection for database
    - Type hints for request/response
    - Docstring for API documentation
    """
    controller = AuthController(db)
    return await controller.register(user_data)

@router.post("/login")
async def login(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse[LoginResponse]:
    """User login"""
    controller = AuthController(db)
    return await controller.login(login_data)

@router.delete("/logout")
async def logout(
    current_user: User = Depends(get_current_user)
) -> SuccessResponse[None]:
    """User logout"""
    # Logout logic (invalidate token, etc.)
    pass

@router.get("/me")
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
) -> SuccessResponse[UserResponse]:
    """Get current user information"""
    return SuccessResponse(
        message="User information retrieved successfully",
        data=UserResponse.from_orm(current_user)
    )

# Main API router (api.py)
from fastapi import APIRouter
from .auth.routes import router as auth_router
from .users.routes import router as users_router
from .profiles.routes import router as profiles_router
from .wounds.images import router as wound_images_router

api_router = APIRouter()

# Include sub-routers with prefixes
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users_router, prefix="/users", tags=["Users"])
api_router.include_router(profiles_router, prefix="/profile", tags=["Profiles"])
api_router.include_router(wound_images_router, prefix="/wound-images", tags=["Wound Images"])
```

### **Bước 7: API Versioning Strategy - Chiến lược phiên bản API**

#### **🤔 Suy luận versioning approach:**
```python
# URL-based versioning (Recommended for REST APIs)
"""
Suy luận: Tại sao URL-based versioning?
- Clear and explicit
- Easy to understand and implement
- Good for breaking changes
- Cacheable by proxies
"""

# Version structure:
"""
/api/v1/users          # Version 1
/api/v2/users          # Version 2 (breaking changes)
/api/v1.1/users        # Minor version (rare)
"""

# Implementation:
from fastapi import FastAPI

app = FastAPI()

# Version 1 API
v1_router = APIRouter(prefix="/api/v1")
v1_router.include_router(auth_router, prefix="/auth")
v1_router.include_router(users_router, prefix="/users")

# Version 2 API (future)
v2_router = APIRouter(prefix="/api/v2")
# v2_router.include_router(auth_v2_router, prefix="/auth")

app.include_router(v1_router)
# app.include_router(v2_router)

# Alternative: Header-based versioning
"""
Accept: application/vnd.skinaid.v1+json
Accept: application/vnd.skinaid.v2+json

Pros: Clean URLs
Cons: Less discoverable, harder to test
"""
```

### **Bước 8: Security & Middleware Integration**

#### **🧠 Suy luận security patterns:**
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Suy luận authentication dependency:
    - Reusable across protected routes
    - Centralized token validation
    - Automatic 401 responses
    """
    token = credentials.credentials
    user = await validate_token(token, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    return user

async def require_admin(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Suy luận authorization dependency:
    - Build on authentication
    - Role-based access control
    - Composable permissions
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

# Protected route examples:
@router.get("/users/me")
async def get_my_profile(
    current_user: User = Depends(get_current_user)
):
    """User must be authenticated"""
    pass

@router.get("/admin/users")
async def list_all_users(
    admin_user: User = Depends(require_admin)
):
    """User must be admin"""
    pass

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: uuid.UUID,
    current_user: User = Depends(get_current_user)
):
    """User can only delete their own account or admin can delete any"""
    if user_id != current_user.id and not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only delete your own account"
        )
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Follow RESTful principles consistently
- Use appropriate HTTP methods and status codes
- Design intuitive URL structures
- Include comprehensive parameter validation
- Organize routes in logical modules
- Implement proper authentication/authorization
- Use API versioning for breaking changes
- Document all endpoints thoroughly

### **❌ DON'T:**
- Use verbs in URLs (use nouns for resources)
- Create overly nested resource hierarchies
- Ignore HTTP semantics
- Skip input validation
- Mix different naming conventions
- Expose internal implementation details
- Create inconsistent response formats

### **🔍 REVIEW CHECKLIST:**
- [ ] URLs follow RESTful conventions?
- [ ] HTTP methods used appropriately?
- [ ] Parameter validation comprehensive?
- [ ] Authentication/authorization implemented?
- [ ] Routes organized logically?
- [ ] API documentation complete?
- [ ] Error handling consistent?
- [ ] Security considerations addressed?

**Remember: Well-designed routes make APIs intuitive and developer-friendly!** 🛣️
