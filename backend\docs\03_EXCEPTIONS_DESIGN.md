# ❌ EXCEPTIONS DESIGN - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn cách suy luận và thiết kế exception system từ error scenarios

---

## 🎯 **QUY TRÌNH THIẾT KẾ EXCEPTIONS**

### **Bước 1: Error Scenario Analysis - Phân tích kịch bản lỗi**

#### **🤔 Câu hỏi cốt lõi:**
```
1. User journey nào có thể fail?
2. System dependencies nào có thể fail?
3. Business rules nào có thể bị vi phạm?
4. External services nào có thể unavailable?
5. Data corruption scenarios nào có thể xảy ra?
```

#### **Ví dụ thực tế: User Registration Flow**
```python
# User Journey Analysis:
"""
Registration Flow:
1. User nhập form → Validation errors
2. Submit form → Network errors
3. Server validate → Business rule violations
4. Create user → Database errors
5. Send welcome email → Email service errors
6. Return response → Serialization errors
"""

# Error Categorization:
error_categories = {
    "Input Validation": [
        "InvalidEmailFormatError",
        "WeakPasswordError", 
        "InvalidUsernameFormatError"
    ],
    "Business Rules": [
        "EmailAlreadyExistsError",
        "UsernameAlreadyExistsError",
        "AccountLimitExceededError"
    ],
    "System Errors": [
        "DatabaseConnectionError",
        "EmailServiceUnavailableError",
        "FileStorageError"
    ],
    "Security Errors": [
        "RateLimitExceededError",
        "SuspiciousActivityError",
        "TokenExpiredError"
    ]
}
```

### **Bước 2: Exception Naming Strategy - Chiến lược đặt tên**

#### **🧠 Suy luận naming conventions:**
```python
# Pattern: [Domain][Problem][Error]
"""
Components:
- Domain: Auth, User, Wound, Payment, etc.
- Problem: AlreadyExists, NotFound, Invalid, Expired, etc.
- Error: Always end with "Error"

Examples:
- EmailAlreadyExistsError = Email + AlreadyExists + Error
- UserNotFoundError = User + NotFound + Error
- WoundAnalysisFailedError = WoundAnalysis + Failed + Error
- PaymentProcessingError = Payment + Processing + Error
"""

# Domain-based organization:
auth_exceptions = [
    "EmailAlreadyExistsError",      # Registration
    "InvalidCredentialsError",      # Login
    "AccountDeactivatedError",      # Account status
    "PasswordExpiredError",         # Security policy
    "TwoFactorRequiredError"        # Security enhancement
]

user_exceptions = [
    "UserNotFoundError",            # User lookup
    "ProfileIncompleteError",       # Business rule
    "InvalidUserDataError",         # Data validation
    "UserPermissionDeniedError"     # Authorization
]

wound_exceptions = [
    "InvalidImageFormatError",      # File validation
    "ImageTooLargeError",          # Size constraint
    "AnalysisFailedError",         # AI processing
    "InsufficientImageQualityError" # Quality check
]
```

#### **🤔 Suy luận specific vs generic exceptions:**
```python
# ✅ Specific exceptions (Better)
class EmailAlreadyExistsError(Exception):
    """Specific error for email duplication"""
    pass

class UsernameAlreadyExistsError(Exception):
    """Specific error for username duplication"""
    pass

# ❌ Generic exceptions (Avoid)
class DuplicateDataError(Exception):
    """Too generic - hard to handle specifically"""
    pass

# Reasoning:
"""
Specific exceptions allow:
1. Targeted error handling in controllers
2. Different user messages per error type
3. Different HTTP status codes
4. Better error analytics and monitoring
5. Clearer code intent
"""
```

### **Bước 3: Exception Hierarchy Design - Thiết kế phân cấp**

#### **🧠 Suy luận inheritance structure:**
```python
# Base exception classes
class SkinAidException(Exception):
    """
    Root exception for all application errors
    
    Suy luận: Tại sao cần base exception?
    - Catch all app exceptions vs system exceptions
    - Common attributes (error_code, message, context)
    - Consistent logging and monitoring
    """
    def __init__(self, message: str, error_code: str = None, context: dict = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.context = context or {}
        super().__init__(message)

class BusinessRuleException(SkinAidException):
    """
    Base for business logic violations
    
    Suy luận: Business errors vs system errors
    - Usually user-recoverable
    - HTTP 400-level status codes
    - User-friendly messages
    """
    def __init__(self, message: str, error_code: str, context: dict = None):
        super().__init__(message, error_code, context)

class SystemException(SkinAidException):
    """
    Base for system-level errors
    
    Suy luận: System errors characteristics
    - Usually not user-recoverable
    - HTTP 500-level status codes
    - Technical messages (logged, not shown to user)
    """
    def __init__(self, message: str, error_code: str, context: dict = None):
        super().__init__(message, error_code, context)

# Domain-specific base classes
class AuthException(BusinessRuleException):
    """Base for authentication/authorization errors"""
    pass

class ValidationException(BusinessRuleException):
    """Base for input validation errors"""
    pass

class ExternalServiceException(SystemException):
    """Base for external service failures"""
    pass
```

#### **🤔 Suy luận concrete exception implementations:**
```python
# Authentication exceptions
class EmailAlreadyExistsError(AuthException):
    """
    Suy luận design decisions:
    - Message: User-friendly, actionable
    - Error code: Unique, trackable
    - HTTP status: 409 Conflict (resource exists)
    """
    def __init__(self, email: str = None):
        super().__init__(
            message="This email is already registered. Please use a different email or try logging in.",
            error_code="AUTH_EMAIL_EXISTS",
            context={"email": email} if email else None
        )

class InvalidCredentialsError(AuthException):
    """
    Suy luận: Security consideration
    - Don't specify if email or password is wrong
    - Generic message to prevent user enumeration
    - Same error for non-existent user and wrong password
    """
    def __init__(self):
        super().__init__(
            message="Invalid email or password. Please check your credentials and try again.",
            error_code="AUTH_INVALID_CREDENTIALS"
        )

class AccountDeactivatedError(AuthException):
    """
    Suy luận: User needs specific guidance
    - Clear message about account status
    - Provide next steps (contact support)
    - Different from invalid credentials
    """
    def __init__(self):
        super().__init__(
            message="Your account has been deactivated. Please contact support for assistance.",
            error_code="AUTH_ACCOUNT_DEACTIVATED"
        )

# Validation exceptions
class WeakPasswordError(ValidationException):
    """
    Suy luận: Educational error message
    - Explain what makes password weak
    - Provide specific requirements
    - Help user create better password
    """
    def __init__(self, requirements: List[str] = None):
        default_requirements = [
            "At least 8 characters long",
            "Contains uppercase and lowercase letters",
            "Contains at least one number",
            "Contains at least one special character"
        ]
        
        super().__init__(
            message="Password does not meet security requirements.",
            error_code="VALIDATION_WEAK_PASSWORD",
            context={"requirements": requirements or default_requirements}
        )

# System exceptions
class DatabaseConnectionError(SystemException):
    """
    Suy luận: System error handling
    - Technical message for logging
    - Generic user message (don't expose internals)
    - Include connection details in context for debugging
    """
    def __init__(self, connection_string: str = None, original_error: str = None):
        super().__init__(
            message="Database connection failed",
            error_code="SYS_DATABASE_CONNECTION",
            context={
                "connection_string": connection_string,
                "original_error": original_error
            }
        )
```

### **Bước 4: Error Code System Design - Thiết kế hệ thống mã lỗi**

#### **🧠 Suy luận error code strategy:**
```python
# Error code format: [DOMAIN]_[CATEGORY]_[SPECIFIC]
"""
Reasoning:
- DOMAIN: Easy to filter logs by module
- CATEGORY: Group related errors
- SPECIFIC: Unique identification

Examples:
AUTH_LOGIN_INVALID_CREDENTIALS
AUTH_REGISTER_EMAIL_EXISTS
USER_PROFILE_INCOMPLETE
WOUND_ANALYSIS_FAILED
SYS_DATABASE_CONNECTION
"""

class ErrorCodes:
    """
    Centralized error code definitions
    
    Suy luận: Tại sao centralize?
    - Avoid duplicate codes
    - Easy to search and reference
    - Consistent naming patterns
    - Documentation in one place
    """
    
    # Authentication errors (AUTH_xxx)
    AUTH_INVALID_CREDENTIALS = "AUTH_INVALID_CREDENTIALS"
    AUTH_EMAIL_EXISTS = "AUTH_EMAIL_EXISTS"
    AUTH_USERNAME_EXISTS = "AUTH_USERNAME_EXISTS"
    AUTH_ACCOUNT_DEACTIVATED = "AUTH_ACCOUNT_DEACTIVATED"
    AUTH_TOKEN_EXPIRED = "AUTH_TOKEN_EXPIRED"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH_INSUFFICIENT_PERMISSIONS"
    
    # Validation errors (VAL_xxx)
    VAL_WEAK_PASSWORD = "VAL_WEAK_PASSWORD"
    VAL_INVALID_EMAIL = "VAL_INVALID_EMAIL"
    VAL_INVALID_USERNAME = "VAL_INVALID_USERNAME"
    VAL_REQUIRED_FIELD = "VAL_REQUIRED_FIELD"
    VAL_INVALID_FORMAT = "VAL_INVALID_FORMAT"
    
    # User errors (USER_xxx)
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_PROFILE_INCOMPLETE = "USER_PROFILE_INCOMPLETE"
    USER_PERMISSION_DENIED = "USER_PERMISSION_DENIED"
    
    # Wound analysis errors (WOUND_xxx)
    WOUND_INVALID_IMAGE = "WOUND_INVALID_IMAGE"
    WOUND_IMAGE_TOO_LARGE = "WOUND_IMAGE_TOO_LARGE"
    WOUND_ANALYSIS_FAILED = "WOUND_ANALYSIS_FAILED"
    WOUND_POOR_IMAGE_QUALITY = "WOUND_POOR_IMAGE_QUALITY"
    
    # System errors (SYS_xxx)
    SYS_DATABASE_CONNECTION = "SYS_DATABASE_CONNECTION"
    SYS_EXTERNAL_SERVICE = "SYS_EXTERNAL_SERVICE"
    SYS_FILE_STORAGE = "SYS_FILE_STORAGE"
    SYS_INTERNAL_ERROR = "SYS_INTERNAL_ERROR"
```

### **Bước 5: Error Message Design - Thiết kế thông báo lỗi**

#### **🤔 Suy luận user-friendly messages:**
```python
class Messages:
    """
    Centralized user-friendly messages
    
    Suy luận principles:
    1. Clear and specific
    2. Actionable (tell user what to do)
    3. Empathetic tone
    4. No technical jargon
    5. Consistent voice
    """
    
    # ✅ Good messages (Clear, actionable)
    EMAIL_ALREADY_EXISTS = "This email is already registered. Please use a different email or try logging in."
    WEAK_PASSWORD = "Your password needs to be stronger. Please include uppercase letters, numbers, and special characters."
    INVALID_CREDENTIALS = "We couldn't find an account with those credentials. Please check your email and password."
    
    # ❌ Bad messages (Avoid these)
    # "Error occurred" - Too vague
    # "Invalid input" - Not specific
    # "Database constraint violation" - Too technical
    # "Authentication failed" - Not actionable
    
    # Context-aware messages
    @staticmethod
    def get_validation_message(field: str, rule: str, context: dict = None) -> str:
        """Generate contextual validation messages"""
        templates = {
            "required": f"{field.title()} is required.",
            "min_length": f"{field.title()} must be at least {context.get('min_length', 'X')} characters long.",
            "max_length": f"{field.title()} cannot exceed {context.get('max_length', 'X')} characters.",
            "format": f"Please enter a valid {field.lower()}.",
            "unique": f"This {field.lower()} is already taken. Please choose a different one."
        }
        return templates.get(rule, f"Invalid {field.lower()}.")
```

### **Bước 6: Exception Handling Patterns - Mẫu xử lý lỗi**

#### **🧠 Suy luận handling strategies:**
```python
# Service layer exception handling
class AuthService:
    async def create_user(self, user_data: UserCreate) -> User:
        """
        Suy luận: Service layer nên throw exceptions
        - Let controller handle HTTP responses
        - Keep business logic clean
        - Enable specific error handling
        """
        try:
            # Validation
            if await self.email_exists(user_data.email):
                raise EmailAlreadyExistsError(user_data.email)
            
            if not self.is_password_strong(user_data.password):
                raise WeakPasswordError()
            
            # Business logic
            user = await self.create_user_record(user_data)
            return user
            
        except (EmailAlreadyExistsError, WeakPasswordError):
            # Re-raise business exceptions
            raise
        except Exception as e:
            # Wrap unexpected errors
            logger.error(f"Unexpected error in create_user: {str(e)}")
            raise SystemException(
                message="An unexpected error occurred",
                error_code=ErrorCodes.SYS_INTERNAL_ERROR,
                context={"original_error": str(e)}
            )

# Controller layer exception handling
class AuthController:
    async def register(self, user_data: UserCreate):
        """
        Suy luận: Controller converts exceptions to HTTP responses
        - Specific handling for known errors
        - Generic handling for unexpected errors
        - Consistent response format
        """
        try:
            user = await self.auth_service.create_user(user_data)
            return SuccessResponse(
                message="Registration successful",
                data=UserResponse.from_orm(user)
            ), 201
            
        except EmailAlreadyExistsError as e:
            return ErrorResponse(
                message=e.message,
                error_code=e.error_code,
                error_details=e.context
            ), 409  # Conflict
            
        except WeakPasswordError as e:
            return ErrorResponse(
                message=e.message,
                error_code=e.error_code,
                error_details=e.context
            ), 400  # Bad Request
            
        except SystemException as e:
            # Log system errors but don't expose details
            logger.error(f"System error: {e.message}", extra=e.context)
            return ErrorResponse(
                message="We're experiencing technical difficulties. Please try again later.",
                error_code="INTERNAL_ERROR"
            ), 500
            
        except Exception as e:
            # Catch-all for truly unexpected errors
            logger.error(f"Unhandled error in register: {str(e)}")
            return ErrorResponse(
                message="An unexpected error occurred",
                error_code="UNKNOWN_ERROR"
            ), 500
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Analyze all possible error scenarios
- Use specific exception classes for different errors
- Create clear inheritance hierarchy
- Centralize error codes and messages
- Include context information in exceptions
- Log system errors with details
- Return user-friendly error messages
- Use appropriate HTTP status codes

### **❌ DON'T:**
- Use generic exception classes
- Expose technical details to users
- Create overly complex exception hierarchies
- Duplicate error codes across domains
- Return different error formats
- Ignore error context and debugging info
- Use exceptions for control flow

### **🔍 REVIEW CHECKLIST:**
- [ ] All error scenarios identified and handled?
- [ ] Exception naming follows consistent patterns?
- [ ] Error messages are user-friendly and actionable?
- [ ] Error codes are unique and trackable?
- [ ] System errors are logged with sufficient detail?
- [ ] HTTP status codes match error types?
- [ ] Exception hierarchy is logical and maintainable?

**Remember: Good error handling improves user experience and developer productivity!** ❌
