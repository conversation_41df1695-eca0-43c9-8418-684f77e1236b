
from fastapi import APIRouter, Depends, status
from sqlmodel.ext.asyncio.session import AsyncSession
from typing import Union

from app.shared.schemas.response import SuccessResponse, ErrorResponse
from app.modules.auth.schemas.user import UserCreate, UserLogin, UserResponse, EmailVerificationRequest, EmailVerificationResponse
from app.modules.auth.controllers.auth_controllers import AuthController
from app.api.v1.deps import get_db

router = APIRouter(prefix="/auth", tags=["Authentication"])

async def get_auth_controller(db: AsyncSession = Depends(get_db)) -> AuthController:
    """Tạo AuthController với database session từ deps.py"""
    return AuthController(db)

@router.post(
    "/register",
    response_model=Union[SuccessResponse[UserResponse], ErrorResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Đăng ký user mới",
    description="Tạo tài khoản mới với email và password. Email xác thực sẽ được gửi sau khi đăng ký."
)
async def register_user(
    user_data: UserCreate,
    controller: AuthController = Depends(get_auth_controller)
):
    return await controller.register_user(user_data)

@router.post(
    "/login",
    response_model=Union[SuccessResponse[UserResponse], ErrorResponse],
    summary="Đăng nhập",
    description="Xác thực user và trả về thông tin user"
)
async def login_user(
    credentials: UserLogin,
    controller: AuthController = Depends(get_auth_controller)
):
    return await controller.login_user(credentials)

@router.post(
    "/verify-email",
    response_model=Union[SuccessResponse[EmailVerificationResponse], ErrorResponse],
    summary="Xác thực email",
    description="Xác thực email bằng token được gửi qua email"
)
async def verify_email(
    verification_data: EmailVerificationRequest,
    controller: AuthController = Depends(get_auth_controller)
):
    return await controller.verify_email(verification_data)

@router.get(
    "/health",
    response_model=SuccessResponse[dict],
    summary="Kiểm tra tình trạng service",
    description="Health check cho auth service"
)
async def health_check(
    controller: AuthController = Depends(get_auth_controller)
):
    return await controller.health_check()
