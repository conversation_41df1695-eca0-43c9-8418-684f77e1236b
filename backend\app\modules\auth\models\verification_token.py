from sqlmodel import SQLModel, Field, Column, Relationship
from sqlalchemy import <PERSON><PERSON>ey
import uuid
import sqlalchemy.dialects.postgresql as pg
from datetime import datetime, timedelta
from typing import Optional, TYPE_CHECKING
from app.shared.models.basemodel import TimestampMixin

if TYPE_CHECKING:
    from app.modules.auth.models.user import User


class VerificationToken(TimestampMixin, SQLModel, table=True):
    __tablename__ = "verification_tokens"

    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )

    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), nullable=False, index=True)
    )

    email: str = Field(nullable=False, index=True)
    token: str = Field(nullable=False, unique=True, index=True)

    expires_at: datetime = Field(nullable=False)
    is_used: bool = Field(default=False, nullable=False)

    # Quan hệ 1–N với User
    user: Optional["User"] = Relationship(back_populates="verification_tokens")

    def __repr__(self):
        return f"<VerificationToken {self.email}>"

    @property
    def is_expired(self) -> bool:
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        return not self.is_used and not self.is_expired

    @classmethod
    def create_token(cls, user_id: uuid.UUID, email: str, token: str, hours_valid: int = 24):
        return cls(
            user_id=user_id,
            email=email,
            token=token,
            expires_at=datetime.utcnow() + timedelta(hours=hours_valid)
        )
