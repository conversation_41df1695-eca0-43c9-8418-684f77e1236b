# 📊 LESSON 09: COMPLIANCE & MONITORING - TUÂN THỦ VÀ GIÁM SÁT

**Mục tiêu**: Implement compliance frameworks và comprehensive monitoring cho production JWT systems

---

## 🎯 **PHẦN 1: COMPLIANCE FRAMEWORKS - KHUNG TUÂN THỦ**

### **🏥 HIPAA Compliance Implementation**

```python
# compliance/hipaa.py - HIPAA compliance cho healthcare applications
from datetime import datetime
from typing import Dict, List, Optional
from enum import Enum

class HIPAAEventType(str, Enum):
    """HIPAA audit event types"""
    PHI_ACCESS = "phi_access"
    PHI_CREATION = "phi_creation"
    PHI_MODIFICATION = "phi_modification"
    PHI_DELETION = "phi_deletion"
    USER_AUTHENTICATION = "user_authentication"
    AUTHORIZATION_FAILURE = "authorization_failure"
    SYSTEM_ACCESS = "system_access"
    DATA_EXPORT = "data_export"
    """
    <PERSON><PERSON><PERSON><PERSON> thích HIPAA Event Types:
    - PHI (Protected Health Information) events
    - Authentication và authorization events
    - System access tracking
    - Data export/import activities
    """

class HIPAAAuditLogger:
    """HIPAA-compliant audit logging system"""
    
    def __init__(self):
        self.required_fields = [
            "timestamp", "user_id", "event_type", "resource_accessed",
            "ip_address", "user_agent", "success", "failure_reason"
        ]
    """
    Giải thích HIPAA Audit Requirements:
    - Comprehensive logging of all PHI access
    - User identification và authentication
    - Date và time of access
    - Type of action performed
    - Success hoặc failure of access attempt
    """
    
    async def log_hipaa_event(
        self,
        event_type: HIPAAEventType,
        user_id: Optional[str],
        resource_id: Optional[str],
        ip_address: str,
        user_agent: str,
        success: bool,
        additional_data: Optional[Dict] = None
    ):
        """
        Log HIPAA audit event
        
        Args:
            event_type: Type of HIPAA event
            user_id: User performing action
            resource_id: Resource being accessed
            ip_address: Client IP address
            user_agent: Client user agent
            success: Whether action succeeded
            additional_data: Additional context data
        """
        audit_record = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type.value,
            "user_id": user_id,
            "resource_id": resource_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "success": success,
            "additional_data": additional_data or {}
        }
        
        # Store trong tamper-proof audit log
        await self._store_audit_record(audit_record)
        
        # Real-time alerting cho suspicious activities
        if not success or self._is_suspicious_activity(audit_record):
            await self._send_security_alert(audit_record)
        """
        Giải thích HIPAA Audit Logging:
        1. Capture all required fields
        2. Store trong tamper-proof storage
        3. Real-time alerting cho security events
        4. Immutable audit trail
        """
    
    async def _store_audit_record(self, record: Dict):
        """Store audit record trong compliant storage"""
        # Implementation would use:
        # - Write-only database
        # - Cryptographic signatures
        # - Immutable storage (blockchain/append-only logs)
        pass
    
    def _is_suspicious_activity(self, record: Dict) -> bool:
        """Detect suspicious activities"""
        suspicious_indicators = [
            record.get("ip_address") in self.known_threat_ips,
            record.get("event_type") == HIPAAEventType.AUTHORIZATION_FAILURE.value,
            self._detect_unusual_access_pattern(record),
            self._detect_bulk_data_access(record)
        ]
        
        return any(suspicious_indicators)
        """
        Giải thích Suspicious Activity Detection:
        - Known threat IP addresses
        - Authorization failures
        - Unusual access patterns
        - Bulk data access attempts
        """

# Global HIPAA audit logger
hipaa_logger = HIPAAAuditLogger()
```

### **🔒 GDPR Compliance Implementation**

```python
# compliance/gdpr.py - GDPR compliance implementation
from typing import Dict, List, Optional

class GDPRDataProcessor:
    """GDPR-compliant data processing"""
    
    def __init__(self):
        self.lawful_bases = [
            "consent", "contract", "legal_obligation",
            "vital_interests", "public_task", "legitimate_interests"
        ]
        self.data_retention_periods = {
            "user_profiles": 365 * 2,      # 2 years
            "audit_logs": 365 * 7,        # 7 years
            "session_data": 30,           # 30 days
            "analytics_data": 365 * 2     # 2 years
        }
    """
    Giải thích GDPR Requirements:
    - Lawful basis cho data processing
    - Data retention periods
    - Right to be forgotten
    - Data portability
    """
    
    async def process_data_subject_request(
        self,
        request_type: str,
        user_id: str,
        requester_verification: Dict
    ) -> Dict:
        """
        Process GDPR data subject requests
        
        Args:
            request_type: Type of request (access, rectification, erasure, portability)
            user_id: Subject's user ID
            requester_verification: Identity verification data
        
        Returns:
            Dict với request processing results
        """
        # Verify requester identity
        if not await self._verify_data_subject_identity(user_id, requester_verification):
            return {"status": "denied", "reason": "identity_verification_failed"}
        
        if request_type == "access":
            return await self._handle_data_access_request(user_id)
        elif request_type == "rectification":
            return await self._handle_data_rectification_request(user_id)
        elif request_type == "erasure":
            return await self._handle_data_erasure_request(user_id)
        elif request_type == "portability":
            return await self._handle_data_portability_request(user_id)
        else:
            return {"status": "error", "reason": "invalid_request_type"}
        """
        Giải thích GDPR Request Processing:
        1. Verify data subject identity
        2. Route to appropriate handler
        3. Process request according to GDPR requirements
        4. Return processing results
        """
    
    async def _handle_data_erasure_request(self, user_id: str) -> Dict:
        """
        Handle right to be forgotten request
        
        Args:
            user_id: User ID to erase
        
        Returns:
            Dict với erasure results
        """
        erasure_results = {
            "user_profile": False,
            "session_data": False,
            "analytics_data": False,
            "audit_logs": False  # May be retained cho legal reasons
        }
        
        try:
            # Erase user profile data
            await self._erase_user_profile(user_id)
            erasure_results["user_profile"] = True
            
            # Erase session data
            await self._erase_session_data(user_id)
            erasure_results["session_data"] = True
            
            # Anonymize analytics data
            await self._anonymize_analytics_data(user_id)
            erasure_results["analytics_data"] = True
            
            # Audit logs may be retained cho legal compliance
            # But personal identifiers should be removed
            await self._anonymize_audit_logs(user_id)
            
            return {
                "status": "completed",
                "erasure_results": erasure_results,
                "completion_date": datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "partial_results": erasure_results
            }
        """
        Giải thích Data Erasure Process:
        1. Erase personal data where possible
        2. Anonymize data that must be retained
        3. Maintain audit logs cho legal compliance
        4. Return detailed erasure report
        """
    
    async def _handle_data_portability_request(self, user_id: str) -> Dict:
        """
        Handle data portability request
        
        Args:
            user_id: User ID
        
        Returns:
            Dict với portable data
        """
        portable_data = {
            "user_profile": await self._export_user_profile(user_id),
            "preferences": await self._export_user_preferences(user_id),
            "activity_history": await self._export_activity_history(user_id),
            "export_metadata": {
                "export_date": datetime.utcnow().isoformat(),
                "format": "JSON",
                "version": "1.0"
            }
        }
        
        return {
            "status": "completed",
            "data": portable_data,
            "download_url": await self._generate_secure_download_link(user_id)
        }
        """
        Giải thích Data Portability:
        - Export all user data trong machine-readable format
        - Include metadata about export
        - Provide secure download mechanism
        - Structured format cho easy import
        """

# Global GDPR processor
gdpr_processor = GDPRDataProcessor()
```

## 📊 **PHẦN 2: COMPREHENSIVE MONITORING - GIÁM SÁT TOÀN DIỆN**

### **📈 Security Metrics Dashboard**

```python
# monitoring/security_metrics.py - Security monitoring dashboard
from typing import Dict, List
import asyncio
from datetime import datetime, timedelta

class SecurityMetricsCollector:
    """Collect và analyze security metrics"""
    
    def __init__(self):
        self.metrics_buffer = []
        self.alert_thresholds = {
            "failed_login_rate": 10,      # per minute
            "token_validation_failures": 50,  # per minute
            "suspicious_ip_requests": 100,    # per hour
            "privilege_escalation_attempts": 1  # per hour
        }
    """
    Giải thích Security Metrics:
    - Real-time security event collection
    - Configurable alert thresholds
    - Multiple metric types
    - Automated threat detection
    """
    
    async def collect_authentication_metrics(self) -> Dict:
        """
        Collect authentication-related metrics
        
        Returns:
            Dict với authentication metrics
        """
        now = datetime.utcnow()
        last_hour = now - timedelta(hours=1)
        last_minute = now - timedelta(minutes=1)
        
        metrics = {
            "successful_logins_last_hour": await self._count_events(
                "login_success", last_hour, now
            ),
            "failed_logins_last_hour": await self._count_events(
                "login_failure", last_hour, now
            ),
            "failed_logins_last_minute": await self._count_events(
                "login_failure", last_minute, now
            ),
            "unique_users_last_hour": await self._count_unique_users(
                last_hour, now
            ),
            "login_success_rate": await self._calculate_success_rate(
                last_hour, now
            )
        }
        
        return metrics
        """
        Giải thích Authentication Metrics:
        - Success/failure rates
        - Time-based analysis
        - Unique user tracking
        - Success rate calculation
        """
    
    async def collect_token_metrics(self) -> Dict:
        """
        Collect JWT token-related metrics
        
        Returns:
            Dict với token metrics
        """
        now = datetime.utcnow()
        last_hour = now - timedelta(hours=1)
        
        metrics = {
            "tokens_issued_last_hour": await self._count_events(
                "token_issued", last_hour, now
            ),
            "tokens_refreshed_last_hour": await self._count_events(
                "token_refreshed", last_hour, now
            ),
            "token_validation_failures": await self._count_events(
                "token_validation_failure", last_hour, now
            ),
            "blacklisted_tokens_used": await self._count_events(
                "blacklisted_token_used", last_hour, now
            ),
            "average_token_lifetime": await self._calculate_avg_token_lifetime()
        }
        
        return metrics
        """
        Giải thích Token Metrics:
        - Token issuance rates
        - Refresh patterns
        - Validation failures
        - Security violations
        """
    
    async def detect_security_anomalies(self) -> List[Dict]:
        """
        Detect security anomalies trong metrics
        
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        # Check failed login rate
        failed_login_rate = await self._get_failed_login_rate()
        if failed_login_rate > self.alert_thresholds["failed_login_rate"]:
            anomalies.append({
                "type": "high_failed_login_rate",
                "severity": "high",
                "value": failed_login_rate,
                "threshold": self.alert_thresholds["failed_login_rate"],
                "description": "Unusually high failed login attempts detected"
            })
        
        # Check for suspicious IP patterns
        suspicious_ips = await self._detect_suspicious_ip_patterns()
        for ip_data in suspicious_ips:
            anomalies.append({
                "type": "suspicious_ip_activity",
                "severity": "medium",
                "ip_address": ip_data["ip"],
                "request_count": ip_data["count"],
                "description": f"Suspicious activity from IP {ip_data['ip']}"
            })
        
        # Check for privilege escalation attempts
        escalation_attempts = await self._detect_privilege_escalation()
        if escalation_attempts > self.alert_thresholds["privilege_escalation_attempts"]:
            anomalies.append({
                "type": "privilege_escalation_attempts",
                "severity": "critical",
                "value": escalation_attempts,
                "description": "Privilege escalation attempts detected"
            })
        
        return anomalies
        """
        Giải thích Anomaly Detection:
        1. Check multiple security indicators
        2. Compare against thresholds
        3. Classify severity levels
        4. Generate actionable alerts
        """

# Global metrics collector
security_metrics = SecurityMetricsCollector()
```

### **🚨 Real-time Alerting System**

```python
# monitoring/alerting.py - Real-time security alerting
import asyncio
from typing import Dict, List, Callable
from enum import Enum

class AlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SecurityAlertManager:
    """Manage security alerts và notifications"""
    
    def __init__(self):
        self.alert_handlers = {
            AlertSeverity.LOW: [self._log_alert],
            AlertSeverity.MEDIUM: [self._log_alert, self._send_email_alert],
            AlertSeverity.HIGH: [self._log_alert, self._send_email_alert, self._send_slack_alert],
            AlertSeverity.CRITICAL: [
                self._log_alert, self._send_email_alert, 
                self._send_slack_alert, self._trigger_incident_response
            ]
        }
        self.alert_cooldown = {}  # Prevent alert spam
    """
    Giải thích Alert Management:
    - Severity-based alert routing
    - Multiple notification channels
    - Alert cooldown để prevent spam
    - Escalation procedures
    """
    
    async def process_security_alert(self, alert: Dict):
        """
        Process security alert với appropriate handlers
        
        Args:
            alert: Alert data dictionary
        """
        severity = AlertSeverity(alert.get("severity", "low"))
        alert_key = f"{alert['type']}:{alert.get('ip_address', 'unknown')}"
        
        # Check cooldown
        if self._is_in_cooldown(alert_key):
            return
        
        # Set cooldown
        self._set_cooldown(alert_key, severity)
        
        # Process với appropriate handlers
        handlers = self.alert_handlers.get(severity, [])
        await asyncio.gather(*[handler(alert) for handler in handlers])
        """
        Giải thích Alert Processing:
        1. Determine alert severity
        2. Check cooldown để prevent spam
        3. Route to appropriate handlers
        4. Execute handlers concurrently
        """
    
    async def _log_alert(self, alert: Dict):
        """Log alert to security log"""
        security_logger.warning(
            f"Security Alert: {alert['type']} - {alert['description']}",
            extra=alert
        )
    
    async def _send_email_alert(self, alert: Dict):
        """Send email alert to security team"""
        email_body = f"""
        Security Alert Detected
        
        Type: {alert['type']}
        Severity: {alert['severity']}
        Description: {alert['description']}
        Timestamp: {datetime.utcnow().isoformat()}
        
        Additional Details:
        {json.dumps(alert, indent=2)}
        """
        
        await send_email(
            to=["<EMAIL>"],
            subject=f"Security Alert: {alert['type']}",
            body=email_body
        )
    
    async def _send_slack_alert(self, alert: Dict):
        """Send Slack alert to security channel"""
        slack_message = {
            "channel": "#security-alerts",
            "text": f"🚨 Security Alert: {alert['type']}",
            "attachments": [{
                "color": "danger" if alert['severity'] == "critical" else "warning",
                "fields": [
                    {"title": "Severity", "value": alert['severity'], "short": True},
                    {"title": "Type", "value": alert['type'], "short": True},
                    {"title": "Description", "value": alert['description'], "short": False}
                ]
            }]
        }
        
        await send_slack_message(slack_message)
    
    async def _trigger_incident_response(self, alert: Dict):
        """Trigger automated incident response"""
        incident_actions = {
            "high_failed_login_rate": self._block_suspicious_ips,
            "privilege_escalation_attempts": self._lock_affected_accounts,
            "blacklisted_token_used": self._revoke_user_sessions,
            "suspicious_ip_activity": self._add_ip_to_blocklist
        }
        
        action = incident_actions.get(alert['type'])
        if action:
            await action(alert)
        """
        Giải thích Incident Response:
        - Automated response actions
        - Alert-specific responses
        - Immediate threat mitigation
        - Reduce manual intervention
        """

# Global alert manager
alert_manager = SecurityAlertManager()
```

## 🎯 **LESSON 09 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Compliance & Monitoring Features:**
- **HIPAA Compliance**: Comprehensive audit logging cho healthcare data
- **GDPR Compliance**: Data subject rights implementation
- **Security Metrics**: Real-time security monitoring dashboard
- **Anomaly Detection**: Automated threat detection algorithms
- **Alert Management**: Multi-channel alerting với incident response

### **🧠 Critical Compliance Understanding:**
- **Regulatory Requirements**: HIPAA, GDPR compliance frameworks
- **Audit Trails**: Immutable logging cho compliance
- **Data Subject Rights**: Access, rectification, erasure, portability
- **Security Monitoring**: Proactive threat detection
- **Incident Response**: Automated security responses

### **🔧 Production Compliance Considerations:**
- **Legal Review**: Ensure compliance với local regulations
- **Data Retention**: Implement appropriate retention policies
- **Regular Audits**: Schedule compliance assessments
- **Staff Training**: Security awareness programs
- **Documentation**: Maintain compliance documentation

### **🎯 Next Steps:**
- **Lesson 10**: SkinAid Implementation (Capstone Project)
- **Practice**: Implement compliance measures
- **Testing**: Validate monitoring systems

**Bạn đã có enterprise-grade compliance system! Ready for capstone project?** 🚀
