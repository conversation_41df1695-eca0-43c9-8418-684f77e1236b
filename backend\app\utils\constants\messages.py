"""Success and error messages used throughout the application."""

# Success messages
USER_REGISTERED_SUCCESS = "User registered successfully"
USER_LOGIN_SUCCESS = "Login successful"
USER_LOGOUT_SUCCESS = "Logout successful"

# Error messages
EMAIL_ALREADY_REGISTERED = "Email already registered"
INVALID_EMAIL_PASSWORD = "Invalid email or password"
ACCOUNT_DEACTIVATED = "Account is deactivated"
PASSWORD_TOO_WEAK = "Password does not meet the security requirements"