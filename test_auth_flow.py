#!/usr/bin/env python3
"""
Test script for the new email-only authentication flow
"""

import asyncio
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.modules.auth.schemas.user import User<PERSON><PERSON>, UserLogin, EmailVerificationRequest
from app.modules.auth.services.auth_service import AuthService
from app.modules.auth.controllers.auth_controllers import AuthController
from app.utils.email_service import email_service
from pydantic import ValidationError

def test_schemas():
    """Test all schema validations"""
    print("🧪 Testing Schemas...")
    print("-" * 50)
    
    # Test UserCreate with valid data
    try:
        user_data = UserCreate(
            email="<EMAIL>",
            password="TestPassword123!",
            confirm_password="TestPassword123!"
        )
        print("✅ UserCreate with matching passwords: PASS")
    except Exception as e:
        print(f"❌ UserCreate with matching passwords: FAIL - {e}")
    
    # Test UserCreate with mismatched passwords
    try:
        user_data = UserCreate(
            email="<EMAIL>",
            password="TestPassword123!",
            confirm_password="DifferentPassword!"
        )
        print("❌ UserCreate with mismatched passwords should have failed")
    except ValidationError:
        print("✅ UserCreate with mismatched passwords: CORRECTLY REJECTED")
    except Exception as e:
        print(f"❌ UserCreate with mismatched passwords: UNEXPECTED ERROR - {e}")
    
    # Test UserLogin
    try:
        login_data = UserLogin(
            email="<EMAIL>",
            password="TestPassword123!"
        )
        print("✅ UserLogin schema: PASS")
    except Exception as e:
        print(f"❌ UserLogin schema: FAIL - {e}")
    
    # Test EmailVerificationRequest
    try:
        verify_data = EmailVerificationRequest(
            email="<EMAIL>",
            token="sample-token-123"
        )
        print("✅ EmailVerificationRequest schema: PASS")
    except Exception as e:
        print(f"❌ EmailVerificationRequest schema: FAIL - {e}")

def test_email_service():
    """Test email service functionality"""
    print("\n📧 Testing Email Service...")
    print("-" * 50)
    
    try:
        # Test token generation
        token = email_service.generate_verification_token()
        print(f"✅ Token generation: PASS (length: {len(token)})")
        
        # Test email content creation
        subject, html_body, text_body = email_service.create_verification_email_content(
            "<EMAIL>", token
        )
        print("✅ Email content creation: PASS")
        print(f"   Subject: {subject}")
        print(f"   HTML body length: {len(html_body)}")
        print(f"   Text body length: {len(text_body)}")
        
    except Exception as e:
        print(f"❌ Email service test: FAIL - {e}")

def test_imports():
    """Test all critical imports"""
    print("\n📦 Testing Imports...")
    print("-" * 50)
    
    imports_to_test = [
        ("User Model", "from app.modules.auth.models.user import User"),
        ("VerificationToken Model", "from app.modules.auth.models.verification_token import VerificationToken"),
        ("Auth Schemas", "from app.modules.auth.schemas.user import UserCreate, UserLogin, EmailVerificationRequest"),
        ("Auth Service", "from app.modules.auth.services.auth_service import AuthService"),
        ("Auth Controller", "from app.modules.auth.controllers.auth_controllers import AuthController"),
        ("Auth Router", "from app.modules.auth.routes.auth_routers import router"),
        ("Email Service", "from app.utils.email_service import email_service"),
        ("Main App", "from app.main import app")
    ]
    
    for name, import_statement in imports_to_test:
        try:
            exec(import_statement)
            print(f"✅ {name}: PASS")
        except Exception as e:
            print(f"❌ {name}: FAIL - {e}")

def test_user_model():
    """Test User model functionality"""
    print("\n👤 Testing User Model...")
    print("-" * 50)
    
    try:
        # Test display_name property
        class MockUser:
            def __init__(self, email):
                self.email = email
            
            @property
            def display_name(self):
                return self.email.split('@')[0]
        
        user = MockUser("<EMAIL>")
        display_name = user.display_name
        print(f"✅ Display name generation: PASS ('{display_name}')")
        
        # Test repr
        repr_str = f"<User {user.email}>"
        print(f"✅ User repr format: PASS ('{repr_str}')")
        
    except Exception as e:
        print(f"❌ User model test: FAIL - {e}")

def test_api_endpoints():
    """Test API endpoint definitions"""
    print("\n🌐 Testing API Endpoints...")
    print("-" * 50)
    
    try:
        from app.modules.auth.routes.auth_routers import router
        
        expected_endpoints = [
            ("POST", "/auth/register"),
            ("POST", "/auth/login"), 
            ("POST", "/auth/verify-email"),
            ("GET", "/auth/health")
        ]
        
        actual_endpoints = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = list(route.methods) if route.methods else ['GET']
                for method in methods:
                    if method != 'HEAD':  # Skip HEAD methods
                        actual_endpoints.append((method, route.path))
        
        print(f"✅ Found {len(actual_endpoints)} endpoints:")
        for method, path in actual_endpoints:
            print(f"   - {method} {path}")
        
        # Check if all expected endpoints exist
        missing = []
        for expected in expected_endpoints:
            if expected not in actual_endpoints:
                missing.append(expected)
        
        if missing:
            print(f"❌ Missing endpoints: {missing}")
        else:
            print("✅ All expected endpoints present: PASS")
            
    except Exception as e:
        print(f"❌ API endpoints test: FAIL - {e}")

def main():
    """Run all tests"""
    print("🚀 SkinAid Auth System - Comprehensive Test Suite")
    print("=" * 60)
    
    test_imports()
    test_schemas()
    test_user_model()
    test_email_service()
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("🏁 Test Suite Completed!")
    print("\n💡 Next Steps:")
    print("   1. Run database migration: backend/migrations/remove_username_add_verification.sql")
    print("   2. Configure SMTP settings in backend/app/utils/email_service.py")
    print("   3. Start the server: uvicorn app.main:app --reload")
    print("   4. Test the full registration → verification → login flow")

if __name__ == "__main__":
    main()
