# 👑 LESSON 05: ROLE-BASED ACCESS CONTROL - KIỂM SOÁT TRUY CẬP THEO VAI TRÒ

**<PERSON><PERSON><PERSON> tiêu**: Implement RBAC system với roles, permissions, và authorization decorators

---

## 🎯 **PHẦN 1: RBAC CONCEPTS - KHÁI NIỆM RBAC**

### **🤔 Tại sao cần RBAC?**

```python
# Problem với simple authentication:
"""
CURRENT STATE:
- User login → Get token
- Token valid → Access everything
- No granular control
- All users have same permissions

REAL WORLD NEEDS:
- Admin: Manage users, view all data
- Manager: View team data, create reports  
- Employee: View own data only
- Guest: Read-only access to public data
"""

# RBAC Solution:
"""
ROLE-BASED ACCESS CONTROL:
- Users have Roles
- Roles have Permissions
- Permissions control Actions on Resources
- Hierarchical permission inheritance
"""
```

**Giải thích RBAC Components:**
- **User**: Individual person using system
- **Role**: Job function hoặc responsibility (<PERSON><PERSON>, Manager, Employee)
- **Permission**: Specific action on specific resource (read:users, write:reports)
- **Resource**: System entity (users, reports, files)
- **Action**: Operation (create, read, update, delete)

### **🏗️ RBAC Architecture**

```python
# RBAC Hierarchy:
"""
USER → ROLE → PERMISSIONS → RESOURCES

Example:
User: john_doe
├── Role: manager
    ├── Permission: read:users
    ├── Permission: write:reports
    ├── Permission: read:team_data
    └── Inherited from employee:
        ├── Permission: read:own_profile
        └── Permission: update:own_profile
"""

# Permission Format:
"""
Format: action:resource:scope

Examples:
- read:users:all (read all users)
- write:users:own (write own user data)
- delete:reports:team (delete team reports)
- admin:system:all (admin access to everything)
"""
```

**Giải thích Permission Structure:**
- **Action**: What operation (read, write, delete, admin)
- **Resource**: What entity (users, reports, files)
- **Scope**: What subset (all, own, team, department)

## 📊 **PHẦN 2: DATA MODELS - MÔ HÌNH DỮ LIỆU**

### **👤 Enhanced User Models**

```python
# auth/models.py - Enhanced với RBAC
from enum import Enum
from typing import List, Optional

class UserRole(str, Enum):
    """User roles enum"""
    ADMIN = "admin"
    MANAGER = "manager" 
    EMPLOYEE = "employee"
    GUEST = "guest"
    """
    Giải thích Role Hierarchy:
    - ADMIN: Full system access
    - MANAGER: Team management access
    - EMPLOYEE: Standard user access
    - GUEST: Read-only public access
    """

class Permission(str, Enum):
    """System permissions enum"""
    # User management
    READ_USERS_ALL = "read:users:all"
    READ_USERS_TEAM = "read:users:team"
    READ_USERS_OWN = "read:users:own"
    WRITE_USERS_ALL = "write:users:all"
    WRITE_USERS_OWN = "write:users:own"
    DELETE_USERS_ALL = "delete:users:all"
    
    # Report management
    READ_REPORTS_ALL = "read:reports:all"
    READ_REPORTS_TEAM = "read:reports:team"
    READ_REPORTS_OWN = "read:reports:own"
    WRITE_REPORTS_ALL = "write:reports:all"
    WRITE_REPORTS_TEAM = "write:reports:team"
    WRITE_REPORTS_OWN = "write:reports:own"
    
    # System administration
    ADMIN_SYSTEM = "admin:system:all"
    ADMIN_USERS = "admin:users:all"
    """
    Giải thích Permission Granularity:
    - Specific actions (read, write, delete, admin)
    - Specific resources (users, reports, system)
    - Specific scopes (all, team, own)
    - Easy to check và extend
    """

class UserWithRole(UserResponse):
    """Enhanced user model với role information"""
    role: UserRole
    permissions: List[Permission]
    """
    Giải thích UserWithRole:
    - Extends UserResponse với role info
    - Include computed permissions list
    - Used trong JWT payload và responses
    """

class RolePermissions(BaseModel):
    """Role to permissions mapping"""
    role: UserRole
    permissions: List[Permission]
    inherits_from: Optional[UserRole] = None
    """
    Giải thích RolePermissions:
    - Define permissions cho each role
    - Support role inheritance
    - Centralized permission management
    """
```

### **🗄️ Role Permission Mapping**

```python
# auth/rbac.py - Role-based access control logic
from typing import Dict, List, Set
from .models import UserRole, Permission

# Role hierarchy definition
ROLE_HIERARCHY: Dict[UserRole, Optional[UserRole]] = {
    UserRole.GUEST: None,           # No inheritance
    UserRole.EMPLOYEE: UserRole.GUEST,    # Inherits guest permissions
    UserRole.MANAGER: UserRole.EMPLOYEE,  # Inherits employee permissions  
    UserRole.ADMIN: UserRole.MANAGER,     # Inherits manager permissions
}
"""
Giải thích Role Hierarchy:
- GUEST: Base level, minimal permissions
- EMPLOYEE: Inherits guest + employee permissions
- MANAGER: Inherits employee + manager permissions
- ADMIN: Inherits all permissions
"""

# Base permissions cho each role
ROLE_PERMISSIONS: Dict[UserRole, List[Permission]] = {
    UserRole.GUEST: [
        Permission.READ_USERS_OWN,
        Permission.READ_REPORTS_OWN,
    ],
    UserRole.EMPLOYEE: [
        Permission.WRITE_USERS_OWN,
        Permission.WRITE_REPORTS_OWN,
    ],
    UserRole.MANAGER: [
        Permission.READ_USERS_TEAM,
        Permission.READ_REPORTS_TEAM,
        Permission.WRITE_REPORTS_TEAM,
    ],
    UserRole.ADMIN: [
        Permission.READ_USERS_ALL,
        Permission.WRITE_USERS_ALL,
        Permission.DELETE_USERS_ALL,
        Permission.READ_REPORTS_ALL,
        Permission.WRITE_REPORTS_ALL,
        Permission.ADMIN_SYSTEM,
        Permission.ADMIN_USERS,
    ]
}
"""
Giải thích Permission Assignment:
- Each role có base permissions
- Inheritance adds parent role permissions
- ADMIN có all permissions
- Granular control cho each operation
"""

def get_role_permissions(role: UserRole) -> Set[Permission]:
    """
    Get all permissions cho role (including inherited)
    
    Args:
        role: User role
    
    Returns:
        Set of all permissions cho role
    """
    permissions = set()
    current_role = role
    
    # Collect permissions từ role hierarchy
    while current_role is not None:
        role_perms = ROLE_PERMISSIONS.get(current_role, [])
        permissions.update(role_perms)
        current_role = ROLE_HIERARCHY.get(current_role)
    
    return permissions
    """
    Giải thích Permission Collection:
    1. Start với requested role
    2. Add role's base permissions
    3. Move to parent role
    4. Repeat until no parent
    5. Return combined permission set
    """

def has_permission(user_role: UserRole, required_permission: Permission) -> bool:
    """
    Check if role has specific permission
    
    Args:
        user_role: User's role
        required_permission: Permission to check
    
    Returns:
        bool: True nếu role có permission
    """
    user_permissions = get_role_permissions(user_role)
    return required_permission in user_permissions
    """
    Giải thích Permission Check:
    - Get all permissions cho user role
    - Check if required permission trong set
    - Simple boolean result
    - Used trong authorization decorators
    """

def can_access_resource(user_role: UserRole, action: str, resource: str, scope: str = "own") -> bool:
    """
    Check if user can perform action on resource với scope
    
    Args:
        user_role: User's role
        action: Action to perform (read, write, delete)
        resource: Resource to access (users, reports)
        scope: Access scope (own, team, all)
    
    Returns:
        bool: True nếu access allowed
    """
    # Construct permission string
    permission_str = f"{action}:{resource}:{scope}"
    
    try:
        required_permission = Permission(permission_str)
        return has_permission(user_role, required_permission)
    except ValueError:
        # Permission không exist
        return False
    """
    Giải thích Dynamic Permission Check:
    - Construct permission string từ components
    - Try to create Permission enum
    - Check if user has permission
    - Return False cho unknown permissions
    """
```

## 🔐 **PHẦN 3: AUTHORIZATION DECORATORS**

### **🛡️ Permission Decorators**

```python
# auth/decorators.py - Authorization decorators
from functools import wraps
from fastapi import HTTPException, status, Depends
from typing import List, Callable, Any
from .dependencies import get_current_user
from .models import UserResponse, Permission
from .rbac import has_permission

def require_permissions(*required_permissions: Permission):
    """
    Decorator để require specific permissions
    
    Args:
        *required_permissions: List of required permissions
    
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get current user từ dependency
            current_user: UserResponse = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            """
            Giải thích Authentication Check:
            - Decorator assumes current_user trong kwargs
            - Must be used với get_current_user dependency
            - Raise 401 nếu no user context
            """
            
            # Check permissions
            user_permissions = get_role_permissions(current_user.role)
            for permission in required_permissions:
                if permission not in user_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Permission denied: {permission.value}"
                    )
            """
            Giải thích Permission Check:
            - Get all user permissions
            - Check each required permission
            - Raise 403 Forbidden nếu missing permission
            - Specific error message cho debugging
            """
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
    """
    Giải thích Decorator Pattern:
    - Triple-nested function cho parameterized decorator
    - @require_permissions(Permission.READ_USERS_ALL)
    - Wraps original function với permission check
    - Preserves function metadata với @wraps
    """

def require_role(*required_roles: UserRole):
    """
    Decorator để require specific roles
    
    Args:
        *required_roles: List of required roles
    
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user: UserResponse = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if current_user.role not in required_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role required: {[role.value for role in required_roles]}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
    """
    Giải thích Role Check:
    - Simpler than permission check
    - Check exact role match
    - Useful cho role-specific endpoints
    - Less flexible than permission-based
    """

def admin_required(func: Callable) -> Callable:
    """
    Shorthand decorator cho admin-only endpoints
    """
    return require_role(UserRole.ADMIN)(func)
    """
    Giải thích Shorthand Decorator:
    - Common pattern cho admin endpoints
    - Cleaner syntax: @admin_required
    - Equivalent to @require_role(UserRole.ADMIN)
    """
```

### **🛣️ Protected Routes với RBAC**

```python
# auth/routes.py - Enhanced với RBAC
from .decorators import require_permissions, require_role, admin_required
from .models import Permission, UserRole

@router.get("/users", response_model=List[UserResponse])
@require_permissions(Permission.READ_USERS_ALL)
async def get_all_users(current_user: UserResponse = Depends(get_current_user)):
    """
    Get all users - requires READ_USERS_ALL permission
    Only admins have this permission
    """
    # Implementation to get all users
    return get_all_users_from_db()
    """
    Giải thích Permission-based Route:
    - @require_permissions decorator checks permission
    - Only users với READ_USERS_ALL can access
    - Automatic 403 nếu insufficient permissions
    """

@router.get("/users/team", response_model=List[UserResponse])
@require_permissions(Permission.READ_USERS_TEAM)
async def get_team_users(current_user: UserResponse = Depends(get_current_user)):
    """
    Get team users - requires READ_USERS_TEAM permission
    Managers và admins have this permission
    """
    # Implementation to get team users
    return get_team_users_from_db(current_user.team_id)
    """
    Giải thích Team-scoped Access:
    - Managers can see their team
    - Admins can see all teams
    - Employees cannot access this endpoint
    """

@router.get("/admin/system-stats")
@admin_required
async def get_system_stats(current_user: UserResponse = Depends(get_current_user)):
    """
    Admin-only endpoint cho system statistics
    """
    return {"stats": "admin-only data"}
    """
    Giải thích Admin-only Route:
    - @admin_required shorthand decorator
    - Only ADMIN role can access
    - Simple role-based protection
    """

@router.put("/users/{user_id}")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Update user - dynamic permission check
    """
    # Check if updating own profile
    if user_id == current_user.id:
        required_permission = Permission.WRITE_USERS_OWN
    else:
        required_permission = Permission.WRITE_USERS_ALL
    
    # Check permission
    if not has_permission(current_user.role, required_permission):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    # Update user
    return update_user_in_db(user_id, user_data)
    """
    Giải thích Dynamic Permission Check:
    - Different permissions cho own vs other users
    - Runtime permission determination
    - Flexible authorization logic
    - More complex but more precise
    """
```

## 🎯 **LESSON 05 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Components Implemented:**
- **Role Hierarchy**: GUEST → EMPLOYEE → MANAGER → ADMIN
- **Permission System**: Granular action:resource:scope format
- **Permission Inheritance**: Roles inherit parent permissions
- **Authorization Decorators**: Reusable permission checks
- **Dynamic Authorization**: Runtime permission determination

### **🧠 Critical Understanding:**
- **RBAC Benefits**: Scalable, maintainable access control
- **Permission Granularity**: Balance between flexibility và complexity
- **Role Inheritance**: Reduces permission duplication
- **Decorator Pattern**: Clean separation of authorization logic

### **🔧 Production Considerations:**
- **Database Storage**: Store roles và permissions trong DB
- **Permission Caching**: Cache permission lookups
- **Audit Logging**: Track permission checks và failures
- **Dynamic Roles**: Support runtime role assignment

### **🎯 Next Steps:**
- **Lesson 06**: Security Hardening
- **Practice**: Implement complex permission scenarios
- **Testing**: Verify authorization logic thoroughly

**Bạn đã có complete RBAC system! Ready for security hardening?** 🚀
