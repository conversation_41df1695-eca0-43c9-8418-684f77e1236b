# 🏗️ DESIGN PRINCIPLES - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn các nguyên tắc thiết kế cốt lõi để xây dựng backend maintainable và scalable

---

## 🎯 **CÁC NGUYÊN TẮC THIẾT KẾ CỐT LÕI**

### **1. SOLID Principles - Nguyên tắc SOLID**

#### **🤔 Single Responsibility Principle (SRP)**
```python
# ❌ Vi phạm SRP - Class làm quá nhiều việc
class UserManager:
    def create_user(self, user_data):
        # Validate data
        if not self.validate_email(user_data.email):
            raise ValueError("Invalid email")
        
        # Hash password
        hashed_password = bcrypt.hash(user_data.password)
        
        # Save to database
        user = User(email=user_data.email, password=hashed_password)
        self.db.add(user)
        
        # Send email
        self.send_welcome_email(user.email)
        
        # Log activity
        self.log_user_creation(user.id)

# ✅ Tuân thủ SRP - Mỗi class c<PERSON> một trách nhiệm
class UserService:
    """Chỉ lo business logic của User"""
    def __init__(self, user_repo, email_service, logger):
        self.user_repo = user_repo
        self.email_service = email_service
        self.logger = logger
    
    def create_user(self, user_data: UserCreate) -> User:
        # Chỉ lo business logic
        validated_data = self.validate_user_data(user_data)
        user = self.user_repo.create(validated_data)
        
        # Delegate to other services
        self.email_service.send_welcome_email(user.email)
        self.logger.log_user_creation(user.id)
        
        return user

class UserRepository:
    """Chỉ lo data access"""
    def create(self, user_data: dict) -> User:
        user = User(**user_data)
        self.db.add(user)
        self.db.commit()
        return user

class EmailService:
    """Chỉ lo email operations"""
    def send_welcome_email(self, email: str):
        # Email sending logic
        pass
```

#### **🧠 Open/Closed Principle (OCP)**
```python
# ✅ Open for extension, closed for modification
from abc import ABC, abstractmethod

class WoundAnalyzer(ABC):
    """Base analyzer - closed for modification"""
    
    @abstractmethod
    def analyze(self, image_data: bytes) -> AnalysisResult:
        pass

class BasicWoundAnalyzer(WoundAnalyzer):
    """Basic implementation"""
    def analyze(self, image_data: bytes) -> AnalysisResult:
        # Basic analysis logic
        return AnalysisResult(wound_type="unknown", confidence=0.5)

class AIWoundAnalyzer(WoundAnalyzer):
    """AI-powered implementation - extension without modification"""
    def __init__(self, ai_model):
        self.ai_model = ai_model
    
    def analyze(self, image_data: bytes) -> AnalysisResult:
        # AI analysis logic
        prediction = self.ai_model.predict(image_data)
        return AnalysisResult(
            wound_type=prediction.wound_type,
            confidence=prediction.confidence,
            severity=prediction.severity
        )

class WoundAnalysisService:
    """Service uses abstraction - can work with any analyzer"""
    def __init__(self, analyzer: WoundAnalyzer):
        self.analyzer = analyzer
    
    def process_wound_image(self, image_data: bytes) -> AnalysisResult:
        return self.analyzer.analyze(image_data)
```

#### **🤔 Dependency Inversion Principle (DIP)**
```python
# ❌ Vi phạm DIP - High-level module phụ thuộc vào low-level module
class AuthService:
    def __init__(self):
        self.db = PostgreSQLDatabase()  # Concrete dependency
        self.email = SMTPEmailService()  # Concrete dependency
    
    def create_user(self, user_data):
        # Tightly coupled to specific implementations
        pass

# ✅ Tuân thủ DIP - Depend on abstractions
from abc import ABC, abstractmethod

class DatabaseRepository(ABC):
    @abstractmethod
    async def create_user(self, user_data: dict) -> User:
        pass
    
    @abstractmethod
    async def get_user_by_email(self, email: str) -> Optional[User]:
        pass

class EmailServiceInterface(ABC):
    @abstractmethod
    async def send_welcome_email(self, email: str, username: str) -> bool:
        pass

class AuthService:
    """High-level module depends on abstractions"""
    def __init__(self, 
                 user_repo: DatabaseRepository,
                 email_service: EmailServiceInterface):
        self.user_repo = user_repo
        self.email_service = email_service
    
    async def create_user(self, user_data: UserCreate) -> User:
        # Business logic không phụ thuộc vào implementation details
        if await self.user_repo.get_user_by_email(user_data.email):
            raise EmailAlreadyExistsError()
        
        user = await self.user_repo.create_user(user_data.dict())
        await self.email_service.send_welcome_email(user.email, user.username)
        return user

# Concrete implementations
class PostgreSQLUserRepository(DatabaseRepository):
    async def create_user(self, user_data: dict) -> User:
        # PostgreSQL specific implementation
        pass

class SMTPEmailService(EmailServiceInterface):
    async def send_welcome_email(self, email: str, username: str) -> bool:
        # SMTP specific implementation
        pass
```

### **2. Separation of Concerns - Tách biệt mối quan tâm**

#### **🧠 Layered Architecture Pattern**
```python
# Presentation Layer (Controllers/Routes)
class AuthController:
    """
    Concerns: HTTP handling, request/response formatting
    NOT concerned with: Business logic, data access
    """
    def __init__(self, auth_service: AuthService):
        self.auth_service = auth_service
    
    async def register(self, request: UserCreate) -> Response:
        try:
            user = await self.auth_service.create_user(request)
            return SuccessResponse(data=UserResponse.from_orm(user))
        except EmailAlreadyExistsError as e:
            return ErrorResponse(message=str(e)), 409

# Business Logic Layer (Services)
class AuthService:
    """
    Concerns: Business rules, workflow orchestration
    NOT concerned with: HTTP details, database specifics
    """
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo
    
    async def create_user(self, user_data: UserCreate) -> User:
        # Business validation
        if await self.user_repo.email_exists(user_data.email):
            raise EmailAlreadyExistsError()
        
        # Business logic
        hashed_password = self.hash_password(user_data.password)
        return await self.user_repo.create({
            **user_data.dict(),
            'hashed_password': hashed_password
        })

# Data Access Layer (Repositories)
class UserRepository:
    """
    Concerns: Data persistence, queries
    NOT concerned with: Business rules, HTTP handling
    """
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, user_data: dict) -> User:
        user = User(**user_data)
        self.db.add(user)
        await self.db.commit()
        return user
```

### **3. Don't Repeat Yourself (DRY) - Không lặp lại**

#### **🤔 Suy luận code reuse strategies:**
```python
# ❌ Code duplication
class UserController:
    async def create_user(self, user_data: UserCreate):
        try:
            user = await self.user_service.create_user(user_data)
            return SuccessResponse(
                message="User created successfully",
                data=UserResponse.from_orm(user)
            ), 201
        except EmailAlreadyExistsError as e:
            logger.error(f"Email exists error: {str(e)}")
            return ErrorResponse(
                message=str(e),
                error_code="EMAIL_EXISTS"
            ), 409
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return ErrorResponse(
                message="Internal server error",
                error_code="INTERNAL_ERROR"
            ), 500

class ProfileController:
    async def update_profile(self, profile_data: ProfileUpdate):
        try:
            profile = await self.profile_service.update_profile(profile_data)
            return SuccessResponse(
                message="Profile updated successfully",
                data=ProfileResponse.from_orm(profile)
            ), 200
        except ProfileNotFoundError as e:
            logger.error(f"Profile not found: {str(e)}")
            return ErrorResponse(
                message=str(e),
                error_code="PROFILE_NOT_FOUND"
            ), 404
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return ErrorResponse(
                message="Internal server error",
                error_code="INTERNAL_ERROR"
            ), 500

# ✅ DRY - Extract common patterns
class BaseController:
    """Base controller với common error handling"""
    
    def handle_service_exceptions(self, operation_name: str):
        """Decorator for common exception handling"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except BusinessException as e:
                    logger.error(f"{operation_name} business error: {str(e)}")
                    return ErrorResponse(
                        message=str(e),
                        error_code=e.error_code
                    ), self.get_status_code(e)
                except Exception as e:
                    logger.error(f"{operation_name} unexpected error: {str(e)}")
                    return ErrorResponse(
                        message="Internal server error",
                        error_code="INTERNAL_ERROR"
                    ), 500
            return wrapper
        return decorator
    
    def create_success_response(self, data, message: str, status_code: int = 200):
        """Common success response creation"""
        return SuccessResponse(message=message, data=data), status_code

class UserController(BaseController):
    @handle_service_exceptions("create_user")
    async def create_user(self, user_data: UserCreate):
        user = await self.user_service.create_user(user_data)
        return self.create_success_response(
            UserResponse.from_orm(user),
            "User created successfully",
            201
        )
```

### **4. KISS Principle - Keep It Simple, Stupid**

#### **🧠 Suy luận simplicity over complexity:**
```python
# ❌ Over-engineered solution
class ComplexUserFactory:
    def __init__(self):
        self.builders = {}
        self.validators = {}
        self.transformers = {}
    
    def register_builder(self, user_type: str, builder):
        self.builders[user_type] = builder
    
    def create_user_with_strategy_pattern(self, user_type: str, data: dict):
        builder = self.builders.get(user_type)
        if not builder:
            raise ValueError(f"No builder for {user_type}")
        
        validator = self.validators.get(user_type)
        transformer = self.transformers.get(user_type)
        
        # Complex chain of operations...
        return builder.build(transformer.transform(validator.validate(data)))

# ✅ Simple, direct solution
class UserService:
    async def create_user(self, user_data: UserCreate) -> User:
        """Simple, straightforward user creation"""
        # Validate
        if await self.user_repo.email_exists(user_data.email):
            raise EmailAlreadyExistsError()
        
        # Create
        hashed_password = bcrypt.hashpw(user_data.password.encode(), bcrypt.gensalt())
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password.decode()
        )
        
        return await self.user_repo.create(user)
```

### **5. YAGNI - You Aren't Gonna Need It**

#### **🤔 Suy luận feature necessity:**
```python
# ❌ Over-engineering for future requirements
class UserService:
    def __init__(self):
        # Complex caching system "for future scalability"
        self.cache_l1 = InMemoryCache()
        self.cache_l2 = RedisCache()
        self.cache_l3 = DatabaseCache()
        
        # Complex event system "for future integrations"
        self.event_bus = EventBus()
        self.event_handlers = []
        
        # Complex permission system "for future roles"
        self.permission_engine = PermissionEngine()
        self.role_hierarchy = RoleHierarchy()
    
    async def create_user(self, user_data):
        # Overly complex implementation for simple requirement
        pass

# ✅ Build what you need now
class UserService:
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo
    
    async def create_user(self, user_data: UserCreate) -> User:
        """Simple implementation for current requirements"""
        if await self.user_repo.email_exists(user_data.email):
            raise EmailAlreadyExistsError()
        
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=self.hash_password(user_data.password)
        )
        
        return await self.user_repo.create(user)
    
    # Add complexity only when needed:
    # - Caching when performance becomes an issue
    # - Events when integrations are required
    # - Complex permissions when roles are needed
```

### **6. Composition over Inheritance - Ưu tiên kết hợp hơn kế thừa**

#### **🧠 Suy luận flexible design:**
```python
# ❌ Deep inheritance hierarchy (rigid)
class BaseUser:
    def __init__(self, email, username):
        self.email = email
        self.username = username

class PremiumUser(BaseUser):
    def __init__(self, email, username, subscription_level):
        super().__init__(email, username)
        self.subscription_level = subscription_level
    
    def can_upload_images(self):
        return True

class AdminUser(PremiumUser):
    def __init__(self, email, username, subscription_level, admin_level):
        super().__init__(email, username, subscription_level)
        self.admin_level = admin_level
    
    def can_delete_users(self):
        return True

# ✅ Composition (flexible)
class User:
    def __init__(self, email: str, username: str):
        self.email = email
        self.username = username
        self.permissions = PermissionSet()
        self.subscription = None
        self.admin_privileges = None

class PermissionSet:
    def __init__(self):
        self.permissions = set()
    
    def add_permission(self, permission: str):
        self.permissions.add(permission)
    
    def has_permission(self, permission: str) -> bool:
        return permission in self.permissions

class Subscription:
    def __init__(self, level: str, features: List[str]):
        self.level = level
        self.features = features

class AdminPrivileges:
    def __init__(self, level: str, capabilities: List[str]):
        self.level = level
        self.capabilities = capabilities

# Usage - flexible composition
def create_premium_user(email: str, username: str) -> User:
    user = User(email, username)
    user.subscription = Subscription("premium", ["image_upload", "ai_analysis"])
    user.permissions.add_permission("upload_images")
    return user

def create_admin_user(email: str, username: str) -> User:
    user = create_premium_user(email, username)
    user.admin_privileges = AdminPrivileges("super", ["delete_users", "view_analytics"])
    user.permissions.add_permission("delete_users")
    return user
```

### **7. Fail Fast - Thất bại nhanh**

#### **🤔 Suy luận early error detection:**
```python
# ✅ Validate early, fail fast
class UserService:
    async def create_user(self, user_data: UserCreate) -> User:
        # Fail fast - validate immediately
        self._validate_user_data(user_data)
        
        # Check business constraints early
        if await self.user_repo.email_exists(user_data.email):
            raise EmailAlreadyExistsError(user_data.email)
        
        if await self.user_repo.username_exists(user_data.username):
            raise UsernameAlreadyExistsError(user_data.username)
        
        # Only proceed if all validations pass
        return await self._create_user_record(user_data)
    
    def _validate_user_data(self, user_data: UserCreate):
        """Validate data structure and business rules early"""
        if not user_data.email or '@' not in user_data.email:
            raise InvalidEmailError("Email format is invalid")
        
        if len(user_data.password) < 8:
            raise WeakPasswordError("Password must be at least 8 characters")
        
        if not re.match(r'^[a-zA-Z0-9_-]+$', user_data.username):
            raise InvalidUsernameError("Username contains invalid characters")

# ❌ Late validation (problems discovered late)
class BadUserService:
    async def create_user(self, user_data: UserCreate) -> User:
        # Start expensive operations without validation
        hashed_password = await self.expensive_hash_operation(user_data.password)
        
        # Create user record
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password
        )
        
        try:
            await self.user_repo.create(user)
        except IntegrityError:
            # Discover problems late - after expensive operations
            raise EmailAlreadyExistsError()
```

---

## 🎯 **DESIGN PRINCIPLES SUMMARY**

### **✅ APPLY THESE PRINCIPLES:**
- **Single Responsibility**: One class, one reason to change
- **Open/Closed**: Open for extension, closed for modification
- **Dependency Inversion**: Depend on abstractions, not concretions
- **Separation of Concerns**: Each layer has distinct responsibilities
- **DRY**: Extract common patterns and reusable components
- **KISS**: Simple solutions over complex ones
- **YAGNI**: Build what you need now, not what you might need
- **Composition**: Flexible composition over rigid inheritance
- **Fail Fast**: Validate early, catch errors immediately

### **🔍 DESIGN REVIEW QUESTIONS:**
- [ ] Does each class have a single, clear responsibility?
- [ ] Can I extend functionality without modifying existing code?
- [ ] Are dependencies injected rather than hardcoded?
- [ ] Are concerns properly separated across layers?
- [ ] Have I eliminated code duplication?
- [ ] Is the solution as simple as possible?
- [ ] Am I building only what's needed now?
- [ ] Am I using composition effectively?
- [ ] Do I validate inputs early and fail fast?

**Remember: Good design principles lead to maintainable, scalable, and testable code!** 🏗️
