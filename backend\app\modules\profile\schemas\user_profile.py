from pydantic import BaseModel
import enum
from typing import Optional
from datetime import date


class GenderEnum(str, enum.Enum):
    male = "male"
    female = "female"
    other = "other"


class UserProfileBase(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[GenderEnum] = None
    address: Optional[str] = None
    avatar_url: Optional[str] = None


class UserProfileUpdate(UserProfileBase):
    pass


class UserProfileResponse(BaseModel):
    full_name: Optional[str]
    phone: Optional[str]
    date_of_birth: Optional[date]
    gender: Optional[GenderEnum]
    address: Optional[str]

    class Config: 
        from_attributes = True
