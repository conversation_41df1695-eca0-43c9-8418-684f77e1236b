# 💻 LESSON 02: FASTAPI JWT BASICS - TRIỂN KHAI JWT CƠ BẢN

**Mục tiêu**: Implement JWT authentication trong FastAPI từ scratch với giải thích chi tiết

---

## 🎯 **PHẦN 1: SETUP & DEPENDENCIES - THIẾT LẬP THƯ VIỆN**

### **🔧 Required Libraries - Thư viện cần thiết**

```python
# requirements.txt
"""
fastapi==0.104.1
uvicorn==0.24.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
bcrypt==4.0.1
"""

# Install command
"""
pip install fastapi uvicorn python-jose[cryptography] python-multipart bcrypt
"""
```

**Giải thích từng thư viện:**
- **fastapi**: Web framework hiện đại, async, với automatic API documentation
- **uvicorn**: ASGI server để chạy FastAPI applications
- **python-jose**: JWT library cho Python, hỗ trợ nhiều algorithms
- **python-multipart**: Xử lý form data và file uploads
- **bcrypt**: Password hashing library, secure và slow by design

### **🏗️ Project Structure - Cấu trúc dự án**

```python
# Cấu trúc thư mục
"""
jwt_tutorial/
├── main.py              # FastAPI app entry point
├── auth/
│   ├── __init__.py
│   ├── models.py        # Pydantic models
│   ├── security.py      # JWT utilities
│   ├── routes.py        # Authentication routes
│   └── dependencies.py  # FastAPI dependencies
├── database/
│   ├── __init__.py
│   └── fake_db.py       # Fake database for tutorial
└── requirements.txt
```

**Giải thích cấu trúc:**
- **main.py**: Entry point, khởi tạo FastAPI app và include routers
- **auth/**: Module chứa tất cả authentication logic
- **models.py**: Pydantic schemas cho request/response validation
- **security.py**: JWT creation, verification, password hashing utilities
- **dependencies.py**: FastAPI dependency injection cho authentication
- **fake_db.py**: In-memory database cho tutorial (production sẽ dùng real DB)

## 🔐 **PHẦN 2: SECURITY UTILITIES - TIỆN ÍCH BẢO MẬT**

### **🛡️ Password Hashing - Mã hóa mật khẩu**

```python
# auth/security.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
"""
Giải thích CryptContext:
- schemes=["bcrypt"]: Sử dụng bcrypt algorithm
- deprecated="auto": Tự động upgrade old hashes
- bcrypt là slow hashing algorithm, designed để chống brute force
"""

# Secret key for JWT signing (CHANGE IN PRODUCTION!)
SECRET_KEY = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
"""
Giải thích constants:
- SECRET_KEY: Dùng để sign JWT tokens. PHẢI thay đổi trong production!
- ALGORITHM: HS256 = HMAC with SHA-256, symmetric key algorithm
- ACCESS_TOKEN_EXPIRE_MINUTES: Token sống 30 phút, balance giữa security và UX
"""

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify plain password against hashed password
    
    Args:
        plain_password: Password user nhập vào
        hashed_password: Hashed password từ database
    
    Returns:
        bool: True nếu password đúng
    """
    return pwd_context.verify(plain_password, hashed_password)
    """
    Giải thích verify process:
    1. bcrypt extract salt từ hashed_password
    2. Hash plain_password với cùng salt
    3. So sánh kết quả với hashed_password
    4. Constant-time comparison để tránh timing attacks
    """

def get_password_hash(password: str) -> str:
    """
    Hash password using bcrypt
    
    Args:
        password: Plain text password
    
    Returns:
        str: Hashed password với salt
    """
    return pwd_context.hash(password)
    """
    Giải thích hashing process:
    1. bcrypt generate random salt
    2. Combine password + salt
    3. Hash nhiều rounds (default 12 rounds)
    4. Return salt + hash combined string
    
    Tại sao bcrypt tốt:
    - Adaptive: Có thể tăng rounds khi hardware mạnh hơn
    - Salt: Mỗi password có unique salt
    - Slow: Designed để chậm, chống brute force
    """
```

### **🔑 JWT Token Management - Quản lý JWT Token**

```python
# Tiếp tục trong auth/security.py

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Tạo JWT access token
    
    Args:
        data: Dictionary chứa claims để đưa vào token
        expires_delta: Custom expiration time (optional)
    
    Returns:
        str: JWT token string
    """
    to_encode = data.copy()
    """
    Giải thích copy():
    - Tạo shallow copy của data để không modify original dict
    - Đảm bảo function không có side effects
    """
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    """
    Giải thích expiration logic:
    - Sử dụng UTC time để tránh timezone issues
    - Default 30 minutes, có thể override với expires_delta
    - Expiration là security measure quan trọng
    """
    
    to_encode.update({"exp": expire})
    """
    Giải thích "exp" claim:
    - "exp" là standard JWT claim cho expiration time
    - Phải là Unix timestamp (seconds since epoch)
    - JWT libraries tự động check expiration khi decode
    """
    
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
    """
    Giải thích jwt.encode():
    1. Serialize to_encode dict thành JSON
    2. Base64URL encode header và payload
    3. Tạo signature bằng HMAC-SHA256
    4. Combine thành header.payload.signature format
    """

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify và decode JWT token
    
    Args:
        token: JWT token string
    
    Returns:
        Dict với token payload nếu valid, None nếu invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        """
        Giải thích jwt.decode():
        1. Split token thành header, payload, signature
        2. Verify signature bằng SECRET_KEY
        3. Check expiration time
        4. Return payload nếu tất cả checks pass
        
        algorithms=[ALGORITHM]: Chỉ accept HS256, tránh algorithm confusion attacks
        """
        return payload
    except JWTError:
        """
        JWTError bao gồm:
        - ExpiredSignatureError: Token đã hết hạn
        - InvalidTokenError: Token format sai
        - InvalidSignatureError: Signature không đúng
        - InvalidKeyError: Secret key sai
        """
        return None
```

## 📊 **PHẦN 3: DATA MODELS - MÔ HÌNH DỮ LIỆU**

### **👤 User Models - Models người dùng**

```python
# auth/models.py
from pydantic import BaseModel, EmailStr
from typing import Optional

class UserBase(BaseModel):
    """Base user model với common fields"""
    email: EmailStr
    username: str
    """
    Giải thích EmailStr:
    - Pydantic type tự động validate email format
    - Requires email-validator package
    - Đảm bảo email format đúng trước khi process
    """

class UserCreate(UserBase):
    """Model cho user registration request"""
    password: str
    """
    Giải thích inheritance:
    - UserCreate inherit email, username từ UserBase
    - Thêm password field cho registration
    - Password chỉ cần khi tạo user, không return trong response
    """

class UserResponse(UserBase):
    """Model cho user data trong response"""
    id: int
    is_active: bool
    
    class Config:
        from_attributes = True
    """
    Giải thích Config:
    - from_attributes=True: Cho phép tạo model từ ORM objects
    - Pydantic sẽ access attributes thay vì dict keys
    - Cần thiết khi work với SQLAlchemy models
    """

class Token(BaseModel):
    """Model cho token response"""
    access_token: str
    token_type: str
    """
    Giải thích Token model:
    - access_token: JWT token string
    - token_type: Luôn là "bearer" cho JWT
    - Follows OAuth 2.0 token response format
    """

class TokenData(BaseModel):
    """Model cho token payload data"""
    username: Optional[str] = None
    """
    Giải thích TokenData:
    - Represent data extracted từ JWT payload
    - Optional fields vì token có thể invalid
    - Dùng cho type hints trong dependencies
    """
```

### **🗄️ Fake Database - Database giả lập**

```python
# database/fake_db.py
from typing import Dict, Optional
from auth.models import UserResponse

# Fake users database
fake_users_db: Dict[str, Dict] = {
    "testuser": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        "is_active": True,
    }
}
"""
Giải thích fake database:
- Dict[str, Dict]: Key là username, value là user data
- hashed_password: Bcrypt hash của "secret"
- Production sẽ dùng real database như PostgreSQL
- Fake DB giúp focus vào JWT logic thay vì database setup
"""

def get_user(username: str) -> Optional[Dict]:
    """
    Get user by username từ fake database
    
    Args:
        username: Username để tìm
    
    Returns:
        User dict nếu tìm thấy, None nếu không
    """
    return fake_users_db.get(username)
    """
    Giải thích get() method:
    - dict.get(key) return value nếu key exists, None nếu không
    - Safer than dict[key] vì không raise KeyError
    - Perfect cho optional lookups
    """

def create_user(user_data: Dict) -> Dict:
    """
    Tạo user mới trong fake database
    
    Args:
        user_data: Dict chứa user information
    
    Returns:
        Created user dict
    """
    username = user_data["username"]
    fake_users_db[username] = user_data
    return user_data
    """
    Giải thích create process:
    1. Extract username làm key
    2. Store user_data trong fake_users_db
    3. Return created user
    
    Production considerations:
    - Check duplicate username/email
    - Generate unique ID
    - Handle database errors
    """
```

## 🎯 **LESSON 02 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Components Implemented:**
- **Password Security**: Bcrypt hashing với salt và multiple rounds
- **JWT Creation**: Token generation với expiration và signature
- **JWT Verification**: Token validation với error handling
- **Data Models**: Pydantic schemas cho type safety
- **Fake Database**: Simple storage cho tutorial

### **🧠 Critical Understanding:**
- **Bcrypt Security**: Slow by design, adaptive rounds, unique salts
- **JWT Structure**: Header.Payload.Signature với Base64URL encoding
- **Token Expiration**: Security vs UX balance, UTC timestamps
- **Error Handling**: Graceful degradation khi token invalid

### **🔧 Production Considerations:**
- **Secret Key**: Must be cryptographically secure và stored safely
- **Database**: Replace fake DB với real database
- **Error Messages**: Don't leak sensitive information
- **Rate Limiting**: Prevent brute force attacks

### **🎯 Next Steps:**
- **Lesson 03**: Tạo authentication routes và dependencies
- **Practice**: Experiment với token creation/verification
- **Security**: Research JWT best practices

**Bạn đã có foundation cho JWT implementation! Ready for routes?** 🚀
