# 🧠 DESIGN THINKING CHEATSHEET - FASTAPI BACKEND

**Mục tiêu**: Hướng dẫn tư duy thiết kế backend từ requirements đến implementation

---

## 🎯 **QUY TRÌNH SUY LUẬN TỔNG QUÁT**

### **Bước 1: Phân tích Requirements**
```
🤔 Câu hỏi cốt lõi:
1. User muốn làm gì? → User Stories
2. System cần lưu gì? → Data Models  
3. Lỗi gì có thể xảy ra? → Exception Design
4. API nào cần thiết? → Endpoint Design
5. Bảo mật như thế nào? → Security Design
```

### **Bước 2: Thiết kế từ ngoài vào trong**
```
User Interface → API → Business Logic → Database

1. UI mockup → API endpoints cần gì?
2. API endpoints → Schemas cần gì?
3. Schemas → Services cần gì?
4. Services → Models cần gì?
```

---

## 📊 **THIẾT KẾ DATABASE MODELS**

### **<PERSON><PERSON> luận Entities**
```python
# 🤔 Domain: SkinAid - Ứng dụng phân tích vết thương

# User Stories → Entities:
"Đăng ký tài khoản" → User (authentication data)
"Thông tin cá nhân" → UserProfile (personal data)  
"Upload ảnh vết thương" → WoundImage (file data)
"Phân tích vết thương" → WoundAnalysis (AI results)
"Nhận khuyến nghị" → Recommendation (treatment advice)
```

### **Suy luận Relationships**
```python
# 🤔 Mối quan hệ giữa entities:

User ←→ UserProfile (1:1) - Một user có một profile
User ←→ WoundImage (1:N) - Một user có nhiều ảnh
WoundImage ←→ WoundAnalysis (1:1) - Một ảnh có một phân tích
WoundAnalysis ←→ Recommendation (1:N) - Một phân tích có nhiều khuyến nghị
```

### **Suy luận Attributes**
```python
# 🤔 Entity cần những fields gì?

class User:
    # Authentication (bắt buộc)
    id: UUID           # Unique identifier
    email: str         # Login credential
    username: str      # Display name
    hashed_password    # Security
    
    # Account management
    is_active: bool    # Account status
    is_verified: bool  # Email verification
    
    # Audit trail
    created_at: datetime
    updated_at: datetime
```

---

## 📋 **THIẾT KẾ SCHEMAS**

### **Suy luận Input Schemas**
```python
# 🤔 User cần cung cấp gì để đăng ký?

class UserCreate:
    username: str      # Display name (required)
    email: EmailStr    # Login + contact (required)
    password: str      # Authentication (required)
    
    # 🤔 Tại sao không có full_name?
    # → Không bắt buộc khi đăng ký, có thể cập nhật sau
```

### **Suy luận Output Schemas**
```python
# 🤔 Frontend cần gì sau khi đăng ký thành công?

class UserResponse:
    user_id: UUID      # Để làm API calls tiếp theo
    username: str      # Hiển thị "Chào mừng [username]"
    email: str         # Hiển thị để confirm
    created_at: datetime # "Thành viên từ [date]"
    
    # Profile info (optional)
    full_name: Optional[str]    # Tên thật nếu có
    avatar_url: Optional[str]   # Ảnh đại diện nếu có
```

### **Suy luận Validation Rules**
```python
# 🤔 Dữ liệu hợp lệ khi nào?

username: str = Field(
    min_length=2,      # Tên quá ngắn không có nghĩa
    max_length=50,     # Đủ dài cho tên, không quá dài cho UI
    regex=r'^[a-zA-Z0-9_-]+$'  # Chỉ ký tự an toàn cho URL
)

password: str = Field(
    min_length=8       # NIST security recommendation
    # Complex validation ở Service layer
)
```

---

## ❌ **THIẾT KẾ EXCEPTIONS**

### **Suy luận Error Scenarios**
```python
# 🤔 User Story: "Đăng ký tài khoản"
# Điều gì có thể sai?

1. Email đã tồn tại → EmailAlreadyExistsError
2. Username đã tồn tại → UsernameAlreadyExistsError  
3. Password quá yếu → WeakPasswordError
4. Email sai format → InvalidEmailFormatError
5. Database lỗi → DatabaseConnectionError
```

### **Suy luận Error Naming**
```python
# Pattern: [Domain][Problem][Error]

EmailAlreadyExistsError = Email + Already Exists + Error
WeakPasswordError = Weak + Password + Error
InvalidCredentialsError = Invalid + Credentials + Error
```

### **Suy luận Error Codes**
```python
# 🤔 Tại sao cần error codes?
# → Frontend cần handle programmatically
# → Analytics cần track error patterns
# → Support cần debug issues

AUTH_001 = "INVALID_CREDENTIALS"     # Phổ biến nhất
AUTH_002 = "EMAIL_ALREADY_EXISTS"    # Khi đăng ký
AUTH_003 = "USER_NOT_FOUND"          # Khi tìm user
```

---

## 🔧 **THIẾT KẾ SERVICES**

### **Suy luận Service Operations**
```python
# 🤔 User có thể làm gì với authentication?

class AuthService:
    # CRUD operations
    async def create_user()      # Đăng ký
    async def get_user_by_id()   # Lấy thông tin
    async def update_user()      # Cập nhật
    async def delete_user()      # Xóa tài khoản
    
    # Business operations  
    async def authenticate_user()  # Đăng nhập
    async def change_password()    # Đổi password
    async def reset_password()     # Quên password
```

### **Suy luận Business Flow**
```python
# 🤔 create_user() cần làm gì?

async def create_user(self, user_data: UserCreate):
    # 1. Validate business rules
    if await self.email_exists(user_data.email):
        raise EmailAlreadyExistsError()
    
    # 2. Transform data
    hashed_password = hash_password(user_data.password)
    
    # 3. Create entities
    user = User(...)
    profile = UserProfile(user_id=user.id)
    
    # 4. Persist to database
    # 5. Return result
```

---

## 🎮 **THIẾT KẾ CONTROLLERS**

### **Suy luận Controller Responsibilities**
```python
# 🤔 Controller nên làm gì?

✅ SHOULD DO:
- Receive HTTP requests
- Call service methods  
- Handle service exceptions
- Format API responses
- Set HTTP status codes

❌ SHOULD NOT DO:
- Business logic (→ Service)
- Database operations (→ Service)
- Complex validations (→ Service)
```

### **Suy luận Error Handling**
```python
# 🤔 Làm sao handle errors consistently?

try:
    # Happy path
    result = await service.operation()
    return SuccessResponse(data=result)

except SpecificError as e:
    # Known business errors
    return ErrorResponse(message=e.detail, error_code=e.code)

except Exception as e:
    # Unexpected errors
    logger.error(f"Unexpected: {e}")
    return ErrorResponse(message="Internal error")
```

---

## 🌐 **THIẾT KẾ ROUTES**

### **Suy luận URL Design**
```python
# 🤔 RESTful URL pattern:

POST   /auth/register     # Tạo user mới
POST   /auth/login        # Authenticate user
GET    /auth/me           # Lấy current user info

GET    /profile/me        # Lấy profile hiện tại  
PUT    /profile/me        # Cập nhật profile

POST   /wounds            # Upload wound image
GET    /wounds/{id}       # Lấy wound analysis
```

### **Suy luận HTTP Status Codes**
```python
# 🤔 Khi nào dùng status code nào?

200 OK: Successful GET, PUT
201 Created: Successful POST (tạo resource mới)
400 Bad Request: Validation errors
401 Unauthorized: Authentication required  
403 Forbidden: Permission denied
404 Not Found: Resource không tồn tại
409 Conflict: Resource đã tồn tại
500 Internal: Server errors
```

---

## 🎯 **DESIGN PRINCIPLES**

### **1. Single Responsibility**
```python
# Mỗi class/method chỉ làm 1 việc
class AuthService:
    # Chỉ lo authentication logic
    
class UserProfileService:  
    # Chỉ lo profile management
```

### **2. Separation of Concerns**
```python
# Tách biệt các layer
Routes: HTTP handling
Controllers: Request/Response formatting  
Services: Business logic
Models: Data persistence
```

### **3. Dependency Inversion**
```python
# High-level không phụ thuộc low-level
class AuthController:
    def __init__(self, auth_service: AuthService):
        self.auth_service = auth_service  # Inject dependency
```

---

## 🚀 **PRACTICAL TIPS**

### **Khi thiết kế mới:**
1. **Bắt đầu từ User Stories** → Xác định operations cần thiết
2. **Thiết kế API trước** → Định nghĩa contract
3. **Suy luận error scenarios** → Thiết kế exception handling
4. **Implement từ ngoài vào trong** → Routes → Controllers → Services → Models

### **Khi debug:**
1. **Trace request flow** → Routes → Controllers → Services → Database
2. **Check logs** → Xác định layer nào có vấn đề
3. **Validate assumptions** → Data có đúng format không?
4. **Test từng layer riêng** → Isolate vấn đề

### **Khi refactor:**
1. **Identify code smells** → Duplicate code, long methods, god classes
2. **Extract common patterns** → Base classes, utility functions
3. **Improve error handling** → Consistent exception patterns
4. **Add tests** → Ensure refactoring doesn't break functionality

**Nhớ: Thiết kế tốt là thiết kế đơn giản, dễ hiểu và dễ maintain!** 🎯
