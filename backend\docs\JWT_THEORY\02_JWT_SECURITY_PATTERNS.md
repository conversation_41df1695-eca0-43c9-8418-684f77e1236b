# 🛡️ JWT SECURITY PATTERNS - ENTERPRISE SECURITY ARCHITECTURE

**Mục tiêu**: Hiểu các security patterns, threat models, và defense strategies cho JWT systems

---

## 🎯 **SECURITY THREAT LANDSCAPE**

### **Attack Vector Analysis**

#### **🤔 Comprehensive threat modeling:**
```
Category 1: TOKEN COMPROMISE
├── XSS (Cross-Site Scripting)
│   ├── Stored XSS in user content
│   ├── Reflected XSS in URL parameters
│   ├── DOM-based XSS in client code
│   └── Third-party script injection
├── Network Interception
│   ├── Man-in-the-middle attacks
│   ├── Packet sniffing on unsecured networks
│   ├── SSL/TLS downgrade attacks
│   └── DNS spoofing
└── Client-Side Vulnerabilities
    ├── Malicious browser extensions
    ├── Compromised client devices
    ├── Social engineering attacks
    └── Physical device access

Category 2: CRYPTOGRAPHIC ATTACKS
├── Algorithm Confusion
│   ├── RS256 to HS256 downgrade
│   ├── None algorithm bypass
│   └── Weak algorithm exploitation
├── Key Management Issues
│   ├── Weak secret keys
│   ├── Key exposure in code/logs
│   ├── Key rotation failures
│   └── Shared key compromises
└── Implementation Flaws
    ├── Timing attack vulnerabilities
    ├── Side-channel information leaks
    └── Library security bugs

Category 3: BUSINESS LOGIC ATTACKS
├── Token Replay
│   ├── Stolen token reuse
│   ├── Session fixation
│   └── Concurrent session abuse
├── Privilege Escalation
│   ├── Role claim manipulation
│   ├── Permission boundary bypass
│   └── Administrative access abuse
└── Data Exfiltration
    ├── Excessive claim exposure
    ├── Sensitive data in tokens
    └── Cross-tenant data access
```

### **Risk Assessment Matrix**

#### **🧠 Risk prioritization framework:**
```
Impact vs Probability Analysis:

HIGH IMPACT + HIGH PROBABILITY:
- XSS token theft from localStorage
- Weak secret key compromise
- Missing HTTPS enforcement
Priority: CRITICAL - Address immediately

HIGH IMPACT + MEDIUM PROBABILITY:
- Algorithm confusion attacks
- Token replay after compromise
- Privilege escalation via claims
Priority: HIGH - Address in current sprint

HIGH IMPACT + LOW PROBABILITY:
- Advanced cryptographic attacks
- Hardware security module compromise
- Nation-state level attacks
Priority: MEDIUM - Plan for future

MEDIUM IMPACT + HIGH PROBABILITY:
- Brute force login attempts
- Session enumeration attacks
- Basic reconnaissance
Priority: MEDIUM - Standard protections

Risk Factors:
- Data sensitivity level
- User base size and profile
- Regulatory compliance requirements
- Business continuity impact
- Reputation damage potential
```

## 🔐 **DEFENSE-IN-DEPTH STRATEGIES**

### **Layer 1: Token Design Security**

#### **🤔 Secure token architecture:**
```
Claim Design Principles:

1. MINIMAL DISCLOSURE:
   Include only necessary claims
   ❌ Avoid: PII, sensitive business data
   ✅ Include: User ID, basic roles, expiration

2. CLAIM VALIDATION:
   Validate all claims on every request
   - Expiration time (exp)
   - Not before time (nbf)
   - Issuer validation (iss)
   - Audience validation (aud)

3. STRUCTURED PERMISSIONS:
   Use hierarchical permission model
   Example:
   {
     "permissions": [
       "user:read:own",
       "user:update:own",
       "document:read:shared"
     ]
   }

4. TIME-BASED CONTROLS:
   - Short access token lifetime (15-30 minutes)
   - Appropriate refresh token lifetime (7-30 days)
   - Time-based permission grants
   - Session timeout enforcement

Token Structure Security:
{
  "sub": "user_id_only",           // No PII
  "iat": **********,               // Issue time
  "exp": 1234569690,               // Short expiry
  "jti": "unique_token_id",        // For blacklisting
  "aud": "specific_service",       // Audience restriction
  "scope": ["read", "write"],      // Granular permissions
  "ctx": {                         // Context claims
    "device_id": "hash",
    "session_id": "hash"
  }
}
```

### **Layer 2: Cryptographic Security**

#### **🧠 Cryptographic best practices:**
```
Algorithm Selection Strategy:

Production Recommendations:
1. HMAC-SHA256 (HS256):
   Use case: Single service, shared secret
   Key size: 256 bits minimum
   Key generation: Cryptographically secure random
   Key rotation: Every 90 days

2. RSA-SHA256 (RS256):
   Use case: Microservices, public key verification
   Key size: 2048 bits minimum (4096 recommended)
   Key storage: Hardware Security Module (HSM)
   Key rotation: Annual or on compromise

3. ECDSA-SHA256 (ES256):
   Use case: High-performance, modern systems
   Curve: P-256 (secp256r1)
   Performance: 10x faster than RSA
   Key rotation: Annual or on compromise

Key Management Security:
- Generate keys with cryptographically secure RNG
- Store keys separately from application code
- Use environment variables or key management services
- Implement key rotation procedures
- Monitor key usage and access
- Maintain key backup and recovery procedures

Secret Management Patterns:
1. Environment Variables (Basic):
   export JWT_SECRET="cryptographically_random_256_bit_key"

2. Key Management Service (Recommended):
   AWS KMS, Azure Key Vault, HashiCorp Vault

3. Hardware Security Module (Enterprise):
   FIPS 140-2 Level 3 certified HSMs
```

### **Layer 3: Transport Security**

#### **🤔 Secure communication patterns:**
```
HTTPS Enforcement:
- TLS 1.2 minimum (TLS 1.3 preferred)
- Strong cipher suites only
- HTTP Strict Transport Security (HSTS)
- Certificate pinning for mobile apps

Security Headers:
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'; script-src 'self'
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin

CORS Configuration:
Access-Control-Allow-Origin: https://trusted-domain.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE
Access-Control-Allow-Headers: Authorization, Content-Type
Access-Control-Allow-Credentials: true
Access-Control-Max-Age: 86400

Cookie Security (for refresh tokens):
Set-Cookie: refresh_token=value; 
  HttpOnly; 
  Secure; 
  SameSite=Strict; 
  Path=/auth; 
  Max-Age=2592000
```

### **Layer 4: Application Security**

#### **🧠 Application-level protections:**
```
Input Validation:
- Validate all JWT claims
- Sanitize user inputs
- Implement rate limiting
- Use parameterized queries
- Apply principle of least privilege

Token Storage Security:
Client-Side Storage Decision Matrix:

High Security Applications:
- Access Token: Memory only
- Refresh Token: httpOnly cookie
- Session Duration: Short (< 1 hour)

Standard Applications:
- Access Token: sessionStorage
- Refresh Token: httpOnly cookie
- Session Duration: Medium (1-8 hours)

Low Security Applications:
- Access Token: localStorage
- Refresh Token: localStorage
- Session Duration: Long (days)

Server-Side Security:
- Implement token blacklisting
- Use secure session storage
- Apply rate limiting per user/IP
- Monitor for suspicious patterns
- Implement account lockout policies
```

## 🔄 **ADVANCED SECURITY PATTERNS**

### **Token Binding and Fingerprinting**

#### **🤔 Enhanced token security:**
```
Device Fingerprinting:
Purpose: Bind tokens to specific client characteristics

Fingerprint Components:
- User-Agent string (normalized)
- Accept-Language header
- Screen resolution (for browsers)
- Timezone offset
- IP address subnet (not full IP)

Implementation Pattern:
1. Generate fingerprint on login
2. Include fingerprint hash in token
3. Validate fingerprint on each request
4. Reject tokens with mismatched fingerprints

Security Benefits:
- Prevents token use from different devices
- Detects token theft and replay
- Adds additional authentication factor

Privacy Considerations:
- Use hashed fingerprints, not raw data
- Avoid overly specific fingerprinting
- Comply with privacy regulations
- Allow legitimate device changes

Token Binding Example:
{
  "sub": "user123",
  "exp": **********,
  "device_fp": "sha256_hash_of_fingerprint",
  "bind_ip": "***********/24"  // IP subnet, not exact IP
}
```

### **Anomaly Detection and Response**

#### **🧠 Behavioral security monitoring:**
```
Anomaly Detection Patterns:

1. AUTHENTICATION ANOMALIES:
   - Multiple failed login attempts
   - Login from unusual locations
   - Login at unusual times
   - Multiple concurrent sessions
   - Rapid token refresh patterns

2. USAGE ANOMALIES:
   - Unusual API access patterns
   - High-frequency requests
   - Access to unusual resources
   - Permission escalation attempts
   - Data exfiltration patterns

3. TOKEN ANOMALIES:
   - Token reuse after refresh
   - Expired token usage attempts
   - Invalid signature attempts
   - Algorithm manipulation attempts
   - Claim tampering attempts

Automated Response Strategies:

LEVEL 1 - Monitoring:
- Log security events
- Generate alerts
- Update risk scores
- Continue normal operation

LEVEL 2 - Throttling:
- Apply rate limiting
- Require additional authentication
- Increase monitoring frequency
- Notify security team

LEVEL 3 - Blocking:
- Revoke user tokens
- Block IP addresses
- Require password reset
- Escalate to security team

LEVEL 4 - Lockdown:
- Disable user account
- Revoke all user sessions
- Trigger incident response
- Legal/compliance notification

Risk Scoring Algorithm:
base_risk = user_risk_profile
+ location_risk_factor
+ time_risk_factor
+ device_risk_factor
+ behavior_risk_factor

if risk_score > critical_threshold:
    trigger_level_4_response()
elif risk_score > high_threshold:
    trigger_level_3_response()
elif risk_score > medium_threshold:
    trigger_level_2_response()
else:
    trigger_level_1_response()
```

### **Zero-Trust Architecture Integration**

#### **🤔 Zero-trust JWT patterns:**
```
Zero-Trust Principles for JWT:

1. NEVER TRUST, ALWAYS VERIFY:
   - Validate every token on every request
   - Check permissions for every operation
   - Verify user status in real-time
   - Validate device and location context

2. LEAST PRIVILEGE ACCESS:
   - Grant minimal necessary permissions
   - Use time-limited permissions
   - Implement just-in-time access
   - Regular permission audits

3. ASSUME BREACH:
   - Design for token compromise scenarios
   - Implement rapid revocation capabilities
   - Monitor for lateral movement
   - Segment access by sensitivity

Zero-Trust JWT Implementation:
{
  "sub": "user123",
  "exp": **********,           // Short expiry
  "permissions": [             // Minimal permissions
    "resource:action:scope"
  ],
  "context": {
    "device_trust_level": "high",
    "location_trust_level": "medium",
    "time_trust_level": "high"
  },
  "verification_required": [   // Additional verification needed
    "mfa_for_sensitive_ops",
    "device_verification"
  ]
}

Request Validation Flow:
1. Extract and validate JWT
2. Check user account status
3. Validate device trust level
4. Check location trust level
5. Verify time-based restrictions
6. Validate specific permissions
7. Check for additional verification requirements
8. Log access attempt
9. Allow or deny request
```

## 🎯 **SECURITY IMPLEMENTATION CHECKLIST**

### **✅ Cryptographic Security:**
- [ ] Use strong algorithms (HS256/RS256/ES256)
- [ ] Generate cryptographically secure keys
- [ ] Implement proper key rotation
- [ ] Store keys securely (HSM/KMS)
- [ ] Validate algorithm in token verification

### **✅ Token Security:**
- [ ] Short access token lifetime (< 30 minutes)
- [ ] Minimal claims in tokens
- [ ] Unique token IDs (jti) for blacklisting
- [ ] Proper audience and issuer validation
- [ ] Time-based claim validation

### **✅ Transport Security:**
- [ ] HTTPS enforcement everywhere
- [ ] Strong TLS configuration
- [ ] Security headers implementation
- [ ] Proper CORS configuration
- [ ] Certificate pinning (mobile)

### **✅ Storage Security:**
- [ ] Secure token storage strategy
- [ ] httpOnly cookies for refresh tokens
- [ ] Proper cookie security flags
- [ ] XSS protection measures
- [ ] CSRF protection implementation

### **✅ Monitoring and Response:**
- [ ] Comprehensive security logging
- [ ] Anomaly detection systems
- [ ] Automated threat response
- [ ] Security incident procedures
- [ ] Regular security assessments

**Remember: Security is a process, not a product. Continuously assess and improve your JWT security posture!** 🛡️
