from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager 

from app.core.database import init_db
from app.core.config import settings
from app.api.v1.api import router as api_v1_router


@asynccontextmanager 
async def life_span(app:FastAPI): 
    print("Server start !!!")
    await init_db()
    yield 
    print("Server is stopped !!!")

app = FastAPI(
    title= settings.APP_NAME, 
    version= settings.VERSION, 
    lifespan= life_span, 
)

app.include_router(api_v1_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {
        "app": settings.APP_NAME,
        "version": settings.VERSION,
        "message": "API is running"
    }