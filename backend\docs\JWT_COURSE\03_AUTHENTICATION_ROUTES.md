# 🛣️ LESSON 03: AUTHENTICATION ROUTES - TUYẾN ĐƯỜNG XÁC THỰC

**<PERSON>ục tiêu**: Tạo authentication endpoints và dependencies cho JWT system

---

## 🎯 **PHẦN 1: FASTAPI DEPENDENCIES - DEPENDENCY INJECTION**

### **🔗 Authentication Dependencies - Dependencies xác thực**

```python
# auth/dependencies.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Optional
from .security import verify_token
from .models import TokenData, UserResponse
from database.fake_db import get_user

# HTTP Bearer token scheme
security = HTTPBearer()
"""
Giải thích HTTPBearer:
- FastAPI security scheme cho Bearer tokens
- Tự động extract "Authorization: Bearer <token>" header
- Provide OpenAPI documentation cho authentication
- Return HTTPAuthorizationCredentials object
"""

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserResponse:
    """
    FastAPI dependency để get current authenticated user
    
    Args:
        credentials: HTTPAuthorizationCredentials từ HTTPBearer
    
    Returns:
        UserResponse: Current user information
    
    Raises:
        HTTPException: 401 nếu token invalid
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    """
    Giải thích HTTPException:
    - status_code: HTTP 401 Unauthorized
    - detail: Error message cho client
    - headers: WWW-Authenticate header theo HTTP spec
    - Client sẽ biết cần provide Bearer token
    """
    
    try:
        # Extract token từ credentials
        token = credentials.credentials
        """
        Giải thích credentials object:
        - credentials.scheme: "Bearer"
        - credentials.credentials: actual token string
        - HTTPBearer đã parse "Authorization: Bearer <token>"
        """
        
        # Verify token và extract payload
        payload = verify_token(token)
        if payload is None:
            raise credentials_exception
        """
        Giải thích token verification:
        - verify_token() check signature, expiration, format
        - Return payload dict nếu valid, None nếu invalid
        - None có thể do expired, invalid signature, malformed token
        """
        
        # Extract username từ token payload
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        """
        Giải thích "sub" claim:
        - "sub" = subject, standard JWT claim
        - Thường chứa user identifier (username hoặc user ID)
        - Phải có trong token để identify user
        """
        
        # Create TokenData object
        token_data = TokenData(username=username)
        
    except Exception:
        raise credentials_exception
    """
    Giải thích exception handling:
    - Catch tất cả exceptions trong token processing
    - Return generic error để không leak information
    - Attacker không biết exact reason token failed
    """
    
    # Get user từ database
    user_dict = get_user(username=token_data.username)
    if user_dict is None:
        raise credentials_exception
    """
    Giải thích user lookup:
    - Token valid nhưng user có thể đã bị delete
    - Database lookup đảm bảo user still exists
    - Return 401 nếu user không tồn tại
    """
    
    # Convert dict to UserResponse model
    user = UserResponse(**user_dict)
    return user
    """
    Giải thích model conversion:
    - **user_dict unpacks dict thành keyword arguments
    - Pydantic validate data và create UserResponse object
    - Type safety và data validation
    """

async def get_current_active_user(current_user: UserResponse = Depends(get_current_user)) -> UserResponse:
    """
    Dependency để get current active user (additional check)
    
    Args:
        current_user: User từ get_current_user dependency
    
    Returns:
        UserResponse: Active user
    
    Raises:
        HTTPException: 400 nếu user inactive
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
    """
    Giải thích active user check:
    - Dependency chaining: get_current_active_user depends on get_current_user
    - Additional business logic check
    - User có thể bị deactivate sau khi token issued
    - Separate endpoint cho different access levels
    """
```

### **🔓 Optional Authentication - Xác thực tùy chọn**

```python
# Tiếp tục trong auth/dependencies.py

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[UserResponse]:
    """
    Optional authentication dependency
    Return user nếu token provided và valid, None nếu không
    
    Args:
        credentials: Optional credentials từ HTTPBearer
    
    Returns:
        UserResponse nếu authenticated, None nếu không
    """
    if credentials is None:
        return None
    """
    Giải thích auto_error=False:
    - HTTPBearer(auto_error=False) không raise exception nếu no token
    - Return None thay vì raise 401
    - Useful cho endpoints có thể personalized nếu logged in
    """
    
    try:
        token = credentials.credentials
        payload = verify_token(token)
        if payload is None:
            return None
        
        username: str = payload.get("sub")
        if username is None:
            return None
        
        user_dict = get_user(username=username)
        if user_dict is None:
            return None
        
        user = UserResponse(**user_dict)
        return user if user.is_active else None
        
    except Exception:
        return None
    """
    Giải thích optional authentication:
    - Không raise exceptions, chỉ return None
    - Endpoint có thể handle both authenticated và anonymous users
    - Example: Public posts nhưng show "Like" button nếu logged in
    """
```

## 🛣️ **PHẦN 2: AUTHENTICATION ROUTES - TUYẾN ĐƯỜNG XÁC THỰC**

### **🔐 Login Endpoint - Endpoint đăng nhập**

```python
# auth/routes.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta
from .models import Token, UserCreate, UserResponse
from .security import verify_password, get_password_hash, create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES
from .dependencies import get_current_user, get_current_active_user
from database.fake_db import get_user, create_user

router = APIRouter(prefix="/auth", tags=["authentication"])
"""
Giải thích APIRouter:
- prefix="/auth": Tất cả routes sẽ có /auth prefix
- tags=["authentication"]: Group routes trong OpenAPI docs
- Modular approach, easy to include trong main app
"""

@router.post("/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    User login endpoint
    
    Args:
        form_data: OAuth2PasswordRequestForm với username và password
    
    Returns:
        Token: JWT access token và token type
    
    Raises:
        HTTPException: 401 nếu credentials invalid
    """
    # Authenticate user
    user_dict = get_user(form_data.username)
    """
    Giải thích OAuth2PasswordRequestForm:
    - Standard OAuth2 form data format
    - Fields: username, password, scope (optional)
    - Content-Type: application/x-www-form-urlencoded
    - Compatible với OAuth2 clients
    """
    
    if not user_dict or not verify_password(form_data.password, user_dict["hashed_password"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    """
    Giải thích authentication logic:
    1. Tìm user trong database bằng username
    2. Verify password với stored hash
    3. Nếu fail ở bất kỳ step nào → 401 Unauthorized
    4. Generic error message để không leak information
    """
    
    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user_dict["username"]}, 
        expires_delta=access_token_expires
    )
    """
    Giải thích token creation:
    - data={"sub": username}: "sub" claim với username
    - expires_delta: Custom expiration time
    - Token chứa minimal information (chỉ username)
    - Additional claims có thể add later (role, permissions)
    """
    
    return {"access_token": access_token, "token_type": "bearer"}
    """
    Giải thích response format:
    - Follows OAuth2 token response standard
    - access_token: JWT string
    - token_type: "bearer" (case-insensitive)
    - Client sẽ use: "Authorization: Bearer <access_token>"
    """
```

### **👤 User Registration - Đăng ký người dùng**

```python
# Tiếp tục trong auth/routes.py

@router.post("/register", response_model=UserResponse)
async def register_user(user: UserCreate):
    """
    User registration endpoint
    
    Args:
        user: UserCreate model với user data
    
    Returns:
        UserResponse: Created user information
    
    Raises:
        HTTPException: 400 nếu username đã tồn tại
    """
    # Check if user already exists
    if get_user(user.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    """
    Giải thích duplicate check:
    - Check username uniqueness trước khi create
    - Production cũng cần check email uniqueness
    - Return 400 Bad Request cho client errors
    """
    
    # Hash password
    hashed_password = get_password_hash(user.password)
    """
    Giải thích password hashing:
    - NEVER store plain text passwords
    - bcrypt hash với salt và multiple rounds
    - Irreversible process, can only verify
    """
    
    # Create user data
    user_data = {
        "id": len(fake_users_db) + 1,  # Simple ID generation
        "username": user.username,
        "email": user.email,
        "hashed_password": hashed_password,
        "is_active": True,
    }
    """
    Giải thích user data creation:
    - Simple ID generation cho fake DB
    - Production sẽ dùng auto-increment hoặc UUID
    - is_active=True: User active by default
    - Store hashed_password, not plain password
    """
    
    # Save user to database
    created_user = create_user(user_data)
    
    # Return user response (without password)
    return UserResponse(**created_user)
    """
    Giải thích response:
    - UserResponse model không include password fields
    - Pydantic automatically exclude sensitive data
    - Client receives safe user information
    """
```

### **👤 Protected Routes - Tuyến đường được bảo vệ**

```python
# Tiếp tục trong auth/routes.py

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: UserResponse = Depends(get_current_user)):
    """
    Get current user information
    Requires valid JWT token
    
    Args:
        current_user: Current authenticated user từ dependency
    
    Returns:
        UserResponse: Current user information
    """
    return current_user
    """
    Giải thích protected route:
    - Depends(get_current_user): Require authentication
    - FastAPI automatically call dependency
    - Nếu token invalid → 401 trước khi reach route handler
    - Route handler chỉ run nếu authentication successful
    """

@router.get("/protected")
async def protected_route(current_user: UserResponse = Depends(get_current_active_user)):
    """
    Example protected route requiring active user
    
    Args:
        current_user: Current active user từ dependency
    
    Returns:
        dict: Protected data
    """
    return {"message": f"Hello {current_user.username}, this is protected data!"}
    """
    Giải thích active user requirement:
    - get_current_active_user dependency chain
    - First check authentication, then check active status
    - Inactive users get 400 Bad Request
    - Active users get protected data
    """

@router.get("/public")
async def public_route(current_user: Optional[UserResponse] = Depends(get_current_user_optional)):
    """
    Public route với optional authentication
    
    Args:
        current_user: Optional current user
    
    Returns:
        dict: Public data, personalized nếu logged in
    """
    if current_user:
        return {"message": f"Hello {current_user.username}, welcome back!"}
    else:
        return {"message": "Hello anonymous user!"}
    """
    Giải thích optional authentication:
    - Route accessible without token
    - Personalized experience nếu user logged in
    - Graceful handling của both cases
    - Common pattern cho public content với personalization
    """
```

## 🚀 **PHẦN 3: MAIN APPLICATION - ỨNG DỤNG CHÍNH**

### **🏗️ FastAPI App Setup - Thiết lập ứng dụng**

```python
# main.py
from fastapi import FastAPI
from auth.routes import router as auth_router

app = FastAPI(
    title="JWT Tutorial API",
    description="FastAPI JWT Authentication Tutorial",
    version="1.0.0"
)
"""
Giải thích FastAPI app:
- title, description, version: OpenAPI documentation metadata
- Automatic interactive docs tại /docs và /redoc
- Built-in validation và serialization
"""

# Include authentication router
app.include_router(auth_router)
"""
Giải thích include_router:
- Mount auth_router với /auth prefix
- All routes từ auth_router available trong app
- Modular approach, easy to add more routers
"""

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "JWT Tutorial API is running!"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
    """
    Giải thích uvicorn:
    - ASGI server cho FastAPI
    - host="0.0.0.0": Accept connections từ any IP
    - port=8000: Default development port
    - Auto-reload trong development mode
    """
```

### **🧪 Testing the API - Kiểm thử API**

```python
# Test với curl commands
"""
1. Register new user:
curl -X POST "http://localhost:8000/auth/register" \
     -H "Content-Type: application/json" \
     -d '{"username": "newuser", "email": "<EMAIL>", "password": "newpassword"}'

2. Login to get token:
curl -X POST "http://localhost:8000/auth/login" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=newuser&password=newpassword"

3. Access protected route:
curl -X GET "http://localhost:8000/auth/me" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
"""
```

**Giải thích testing process:**
- **Register**: POST JSON data, receive user info
- **Login**: POST form data (OAuth2 format), receive token
- **Protected**: GET với Authorization header, receive user data
- **Interactive docs**: Visit http://localhost:8000/docs để test graphically

## 🎯 **LESSON 03 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Components Built:**
- **Dependencies**: Authentication dependency injection system
- **Login Route**: OAuth2-compatible token endpoint
- **Registration**: User creation với password hashing
- **Protected Routes**: Various authentication levels
- **Main App**: Complete FastAPI application

### **🧠 Critical Understanding:**
- **Dependency Injection**: FastAPI's powerful DI system
- **OAuth2 Compatibility**: Standard form data format
- **Error Handling**: Consistent 401/400 responses
- **Security Layers**: Multiple authentication levels

### **🔧 Production Readiness:**
- **Database Integration**: Replace fake DB với real database
- **Input Validation**: Enhanced validation rules
- **Rate Limiting**: Prevent brute force attacks
- **Logging**: Audit trails cho security events

### **🎯 Next Steps:**
- **Lesson 04**: Refresh token strategy
- **Practice**: Test all endpoints với different scenarios
- **Security**: Add rate limiting và logging

**Bạn đã có complete JWT authentication system! Ready for advanced features?** 🚀
