# 📊 DATABASE MODELS DESIGN - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn cách suy luận và thiết kế database models từ business requirements

---

## 🎯 **QUY TRÌNH THIẾT KẾ DATABASE MODELS**

### **Bước 1: Domain Analysis - Phân tích miền**

#### **🤔 Câu hỏi cốt lõi:**
```
1. <PERSON>ệ thống này giải quyết vấn đề gì?
2. Ai là users chính?
3. Users muốn làm gì với hệ thống?
4. Dữ liệu nào cần lưu trữ?
5. Dữ liệu nào liên quan đến nhau?
```

#### **Ví dụ thực tế: SkinAid App**
```python
# Domain: Healthcare - Wound Analysis Application

# Business Requirements Analysis:
"""
1. Problem: Người dùng cần phân tích vết thương da
2. Users: <PERSON><PERSON><PERSON>h<PERSON>, b<PERSON><PERSON>, người chăm sóc
3. Core Functions:
   - <PERSON><PERSON><PERSON> ký/đăng nhập tài khoản
   - Upload ảnh vết thương
   - AI phân tích vết thương
   - Nhận khuyến nghị điều trị
   - Theo dõi quá trình hồi phục
"""

# Từ analysis → Identify Entities:
entities = {
    "User": "Người sử dụng hệ thống",
    "UserProfile": "Thông tin cá nhân chi tiết",
    "WoundImage": "Ảnh vết thương được upload",
    "WoundAnalysis": "Kết quả phân tích AI",
    "Recommendation": "Khuyến nghị điều trị",
    "TreatmentHistory": "Lịch sử điều trị"
}
```

### **Bước 2: Entity Identification - Xác định thực thể**

#### **🧠 Phương pháp suy luận:**
```python
# Technique 1: Noun Extraction từ User Stories
"""
User Story: "Là bệnh nhân, tôi muốn upload ảnh vết thương để nhận phân tích"

Nouns identified:
- Bệnh nhân (Patient/User)
- Ảnh (Image) 
- Vết thương (Wound)
- Phân tích (Analysis)
"""

# Technique 2: CRUD Analysis
"""
Dữ liệu nào cần:
- Create (Tạo mới)
- Read (Đọc/Hiển thị)  
- Update (Cập nhật)
- Delete (Xóa)
"""

# Technique 3: Lifecycle Analysis
"""
Vòng đời của một wound analysis:
1. User upload image → WoundImage entity
2. AI process image → WoundAnalysis entity
3. Generate recommendations → Recommendation entity
4. Track progress → TreatmentHistory entity
"""
```

### **Bước 3: Attribute Design - Thiết kế thuộc tính**

#### **🤔 Suy luận attributes cho User entity:**
```python
class User(SQLModel, table=True):
    """
    Suy luận process:
    
    Q: User entity cần lưu gì để support authentication?
    A: id, email, password, username
    
    Q: Cần quản lý account status?
    A: is_active, is_verified, last_login
    
    Q: Cần audit trail?
    A: created_at, updated_at
    
    Q: Cần soft delete?
    A: deleted_at (optional)
    """
    
    # Primary Key - Always UUID for security
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4),
        description="Unique identifier for user"
    )
    
    # Authentication Fields
    email: str = Field(
        unique=True,           # Business rule: One email = One account
        nullable=False,        # Required for login
        index=True,           # Fast lookup during login
        max_length=254,       # RFC 5321 email length limit
        description="User's email address for login"
    )
    
    username: str = Field(
        unique=True,          # Business rule: Unique display name
        nullable=False,       # Required for user identification
        index=True,          # Fast lookup for @mentions, profiles
        min_length=2,        # Minimum meaningful length
        max_length=50,       # UI constraint
        regex=r'^[a-zA-Z0-9_-]+$',  # URL-safe characters only
        description="User's display name"
    )
    
    hashed_password: str = Field(
        nullable=False,       # Required for authentication
        description="Bcrypt hashed password"
    )
    
    # Account Management
    is_active: bool = Field(
        default=True,         # New users are active by default
        nullable=False,       # Must have explicit status
        description="Whether user account is active"
    )
    
    is_verified: bool = Field(
        default=False,        # Require email verification
        nullable=False,       # Must track verification status
        description="Whether user email is verified"
    )
    
    last_login: Optional[datetime] = Field(
        default=None,         # No login initially
        description="Last successful login timestamp"
    )
```

#### **🤔 Suy luận attributes cho WoundImage entity:**
```python
class WoundImage(TimestampMixin, SQLModel, table=True):
    """
    Suy luận process:
    
    Q: Cần lưu gì về uploaded image?
    A: file_path, original_filename, file_size, mime_type
    
    Q: Cần metadata gì cho AI processing?
    A: image_dimensions, upload_source, quality_score
    
    Q: Cần track ownership?
    A: user_id (foreign key)
    
    Q: Cần categorization?
    A: wound_type, body_part, severity_level
    """
    
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )
    
    # Ownership
    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), nullable=False, index=True),
        description="Owner of the wound image"
    )
    
    # File Information
    file_path: str = Field(
        nullable=False,
        max_length=500,       # Long enough for cloud storage paths
        description="Storage path of the image file"
    )
    
    original_filename: str = Field(
        nullable=False,
        max_length=255,       # Standard filename length
        description="Original filename when uploaded"
    )
    
    file_size: int = Field(
        nullable=False,
        gt=0,                 # Must be positive
        description="File size in bytes"
    )
    
    mime_type: str = Field(
        nullable=False,
        max_length=100,       # MIME types are typically short
        description="File MIME type (image/jpeg, image/png, etc.)"
    )
    
    # Image Metadata
    width: Optional[int] = Field(
        default=None,
        gt=0,
        description="Image width in pixels"
    )
    
    height: Optional[int] = Field(
        default=None,
        gt=0,
        description="Image height in pixels"
    )
    
    # Medical Metadata
    body_part: Optional[str] = Field(
        default=None,
        max_length=100,
        description="Body part where wound is located"
    )
    
    wound_type: Optional[str] = Field(
        default=None,
        max_length=100,
        description="Type of wound (cut, burn, bruise, etc.)"
    )
    
    # Processing Status
    processing_status: str = Field(
        default="pending",
        max_length=50,
        description="AI processing status (pending, processing, completed, failed)"
    )
```

### **Bước 4: Relationship Design - Thiết kế mối quan hệ**

#### **🤔 Suy luận relationships:**
```python
# Relationship Analysis Questions:
"""
1. User và UserProfile: 1-to-1 hay 1-to-many?
   → 1-to-1: Một user chỉ có một profile

2. User và WoundImage: 1-to-1 hay 1-to-many?
   → 1-to-many: Một user có thể upload nhiều ảnh

3. WoundImage và WoundAnalysis: 1-to-1 hay 1-to-many?
   → 1-to-1: Một ảnh có một kết quả phân tích

4. WoundAnalysis và Recommendation: 1-to-1 hay 1-to-many?
   → 1-to-many: Một phân tích có thể có nhiều khuyến nghị
"""

# Implementation:
class User(TimestampMixin, SQLModel, table=True):
    # ... fields ...
    
    # Relationships
    profile: Optional["UserProfile"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={
            "uselist": False,        # 1-to-1 relationship
            "cascade": "all, delete-orphan"  # Delete profile when user deleted
        }
    )
    
    wound_images: List["WoundImage"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={
            "cascade": "all, delete-orphan"  # Delete images when user deleted
        }
    )

class WoundImage(TimestampMixin, SQLModel, table=True):
    # ... fields ...
    
    # Relationships
    user: Optional["User"] = Relationship(back_populates="wound_images")
    
    analysis: Optional["WoundAnalysis"] = Relationship(
        back_populates="wound_image",
        sa_relationship_kwargs={
            "uselist": False,
            "cascade": "all, delete-orphan"
        }
    )
```

### **Bước 5: Data Type Selection - Lựa chọn kiểu dữ liệu**

#### **🤔 Suy luận data types:**
```python
# String Fields
email: str = Field(max_length=254)        # RFC standard
username: str = Field(max_length=50)      # UI constraint
description: str = Field(max_length=1000) # Long text
slug: str = Field(max_length=100)         # URL-friendly

# Numeric Fields
file_size: int = Field(gt=0)              # Positive integers
price: Decimal = Field(decimal_places=2)  # Money (avoid float)
rating: float = Field(ge=0, le=5)         # Ratings with range

# Date/Time Fields
created_at: datetime                      # Full timestamp
date_of_birth: date                       # Date only
expires_at: Optional[datetime]            # Optional expiry

# Boolean Fields
is_active: bool = Field(default=True)     # Status flags
is_verified: bool = Field(default=False)  # Verification status

# Enum Fields
status: StatusEnum                        # Controlled vocabulary
priority: PriorityEnum                    # Limited options

# JSON Fields (PostgreSQL)
metadata: Optional[dict] = Field(default=None)  # Flexible data
settings: Optional[dict] = Field(default_factory=dict)  # User preferences
```

### **Bước 6: Indexing Strategy - Chiến lược đánh chỉ mục**

#### **🤔 Suy luận indexing needs:**
```python
# Query Pattern Analysis:
"""
1. Login queries: WHERE email = ? → Index on email
2. Profile lookup: WHERE username = ? → Index on username
3. User's images: WHERE user_id = ? → Index on user_id
4. Recent uploads: ORDER BY created_at DESC → Index on created_at
5. Search by body part: WHERE body_part = ? → Index on body_part
"""

# Index Implementation:
class User(SQLModel, table=True):
    email: str = Field(unique=True, index=True)      # Login queries
    username: str = Field(unique=True, index=True)   # Profile queries

class WoundImage(SQLModel, table=True):
    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), index=True)  # Join queries
    )
    created_at: datetime = Field(index=True)         # Sorting queries
    body_part: Optional[str] = Field(index=True)     # Filter queries

# Composite Indexes (in migration):
"""
CREATE INDEX idx_wound_images_user_created ON wound_images(user_id, created_at DESC);
CREATE INDEX idx_wound_images_status_created ON wound_images(processing_status, created_at DESC);
"""
```

### **Bước 7: Validation & Constraints - Ràng buộc và kiểm tra**

#### **🤔 Suy luận business constraints:**
```python
class User(SQLModel, table=True):
    # Unique constraints
    email: str = Field(unique=True)           # Business rule: One email per account
    username: str = Field(unique=True)        # Business rule: Unique display names
    
    # Length constraints
    username: str = Field(min_length=2, max_length=50)  # Usability constraints
    
    # Format constraints
    email: EmailStr                           # Email format validation
    phone: Optional[str] = Field(regex=r'^\+?[\d\s\-\(\)]+$')  # Phone format

class WoundImage(SQLModel, table=True):
    # Size constraints
    file_size: int = Field(gt=0, le=10_000_000)  # Max 10MB
    
    # Dimension constraints
    width: Optional[int] = Field(gt=0, le=10000)   # Reasonable image size
    height: Optional[int] = Field(gt=0, le=10000)  # Reasonable image size
    
    # Enum constraints
    processing_status: ProcessingStatusEnum     # Limited valid values

# Custom validators
@validator('email')
def validate_email_domain(cls, v):
    """Custom business rule: Only allow certain domains"""
    allowed_domains = ['gmail.com', 'company.com']
    domain = v.split('@')[1]
    if domain not in allowed_domains:
        raise ValueError('Email domain not allowed')
    return v
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Start with business requirements analysis
- Use UUID for primary keys (security)
- Add indexes for frequently queried fields
- Use appropriate data types (Decimal for money, not float)
- Add meaningful constraints and validations
- Include audit fields (created_at, updated_at)
- Use descriptive field names and comments

### **❌ DON'T:**
- Use auto-increment IDs in distributed systems
- Store sensitive data without encryption
- Create tables without considering relationships
- Ignore indexing strategy
- Use VARCHAR without length limits
- Store JSON when relational structure is better

### **🔍 REVIEW CHECKLIST:**
- [ ] All entities identified from business requirements?
- [ ] Relationships correctly defined?
- [ ] Appropriate data types selected?
- [ ] Indexes added for query patterns?
- [ ] Constraints reflect business rules?
- [ ] Audit trail included?
- [ ] Security considerations addressed?

**Remember: Good database design is the foundation of a scalable application!** 🏗️
