from app.utils.constants.regex_patterns import EMAIL_PATTERN, PASSWORD_PATTERN 
from typing import Optional
import re
def validate_password_strength (password: str) -> Optional[str]:
    if not re.match(PASSWORD_PATTERN, password):
        return "Password must be at least 8 characters and contain at least one uppercase letter, " \
        "one lowercase letter, one number, and one special character" 
    return None
    
def validate_email(email: str)-> Optional[str]: 
    if not re.match(EMAIL_PATTERN, email): 
        return "Invalid email format"
    return None 
    
