# 🚀 SKINAID JWT IMPLEMENTATION - STEP-BY-STEP GUIDE

**<PERSON><PERSON><PERSON> tiêu**: Triển khai JWT authentication cho SkinAid project với healthcare compliance và security requirements

---

## 🎯 **IMPLEMENTATION ROADMAP**

### **Phase 1: Core JWT Infrastructure (Days 1-3)**

#### **Day 1: JWT Configuration & Security Setup**

```python
# File: backend/app/core/config.py
from pydantic import BaseSettings
from typing import List

class Settings(BaseSettings):
    # Application
    PROJECT_NAME: str = "SkinAid API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Security - JWT
    SECRET_KEY: str = "CHANGE-THIS-IN-PRODUCTION-256-BIT-KEY"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15  # Short for healthcare security
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7     # Weekly re-authentication
    
    # Security - Cookies
    COOKIE_DOMAIN: str = "localhost"  # Change for production
    COOKIE_SECURE: bool = False       # True in production (HTTPS)
    COOKIE_SAMESITE: str = "strict"   # CSRF protection
    
    # Healthcare Compliance
    HIPAA_COMPLIANT: bool = True
    AUDIT_ALL_REQUESTS: bool = True
    MFA_REQUIRED_ROLES: List[str] = ["professional", "admin"]
    
    # Rate Limiting
    LOGIN_RATE_LIMIT: str = "5/minute"
    API_RATE_LIMIT: str = "100/minute"
    
    # Database
    DATABASE_URL: str = "postgresql+asyncpg://user:pass@localhost/skinaid"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()

# File: backend/app/core/security.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
import secrets
import hashlib
from .config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class SkinAidJWTHandler:
    """Healthcare-compliant JWT handler for SkinAid"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """Create short-lived access token for API requests"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        # Standard claims
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access",
            "jti": secrets.token_urlsafe(16),  # For blacklisting
            "iss": "skinaid-api",
            "aud": "skinaid-client"
        })
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self) -> str:
        """Create long-lived refresh token (stored in database)"""
        return secrets.token_urlsafe(32)
    
    def verify_access_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode access token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Validate token type
            if payload.get("type") != "access":
                return None
            
            # Validate issuer and audience
            if payload.get("iss") != "skinaid-api":
                return None
            if payload.get("aud") != "skinaid-client":
                return None
            
            return payload
        except JWTError:
            return None
    
    def create_device_fingerprint(self, user_agent: str, ip_address: str) -> str:
        """Create device fingerprint for security"""
        fingerprint_data = f"{user_agent}:{ip_address[:12]}"  # Partial IP for privacy
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]

# Password utilities
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# Global instances
jwt_handler = SkinAidJWTHandler()
```

#### **Day 2: Database Models for JWT**

```python
# File: backend/app/modules/auth/models/refresh_token.py
from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects import postgresql as pg
from datetime import datetime
from typing import Optional
import uuid

class RefreshToken(SQLModel, table=True):
    """Refresh token storage for secure session management"""
    __tablename__ = "refresh_tokens"
    
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )
    
    token: str = Field(
        unique=True,
        nullable=False,
        index=True,
        max_length=255,
        description="Refresh token string"
    )
    
    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), nullable=False, index=True),
        description="Token owner"
    )
    
    expires_at: datetime = Field(
        nullable=False,
        index=True,
        description="Token expiration time"
    )
    
    is_active: bool = Field(
        default=True,
        nullable=False,
        description="Token status (for revocation)"
    )
    
    # Security tracking
    device_fingerprint: Optional[str] = Field(
        default=None,
        max_length=32,
        description="Device fingerprint for security"
    )
    
    ip_address: Optional[str] = Field(
        default=None,
        max_length=45,  # IPv6 length
        description="IP address when created"
    )
    
    user_agent: Optional[str] = Field(
        default=None,
        max_length=500,
        description="User agent when created"
    )
    
    # Audit fields
    created_at: datetime = Field(
        default_factory=lambda: datetime.utcnow(),
        nullable=False
    )
    
    last_used_at: Optional[datetime] = Field(
        default=None,
        description="Last time token was used"
    )

# File: backend/app/modules/auth/models/security_audit.py
from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects import postgresql as pg
from sqlalchemy import JSON
from datetime import datetime
from typing import Optional, Dict, Any
import uuid

class SecurityAuditLog(SQLModel, table=True):
    """HIPAA-compliant audit logging for all authentication events"""
    __tablename__ = "security_audit_logs"
    
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )
    
    # Event identification
    event_type: str = Field(
        nullable=False,
        index=True,
        max_length=50,
        description="Type of security event"
    )
    
    event_category: str = Field(
        nullable=False,
        index=True,
        max_length=20,
        description="Event category (auth, access, admin, security)"
    )
    
    # User context
    user_id: Optional[uuid.UUID] = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), nullable=True, index=True),
        description="User involved in event"
    )
    
    user_email: Optional[str] = Field(
        default=None,
        max_length=254,
        index=True,
        description="User email (for failed login attempts)"
    )
    
    # Request context
    ip_address: Optional[str] = Field(
        default=None,
        max_length=45,
        index=True,
        description="Client IP address"
    )
    
    user_agent: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Client user agent"
    )
    
    # Event details
    success: bool = Field(
        nullable=False,
        index=True,
        description="Whether event was successful"
    )
    
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        sa_column=Column(JSON),
        description="Additional event details"
    )
    
    # Compliance fields
    timestamp: datetime = Field(
        nullable=False,
        index=True,
        default_factory=lambda: datetime.utcnow(),
        description="Event timestamp (UTC)"
    )
    
    session_id: Optional[str] = Field(
        default=None,
        max_length=64,
        description="Session identifier"
    )
    
    request_id: Optional[str] = Field(
        default=None,
        max_length=64,
        description="Request identifier for tracing"
    )

# Update: backend/app/modules/auth/models/user.py
# Add role field for RBAC
from enum import Enum

class UserRole(str, Enum):
    PATIENT = "patient"
    PROFESSIONAL = "professional"
    ADMIN = "admin"
    RESEARCHER = "researcher"  # Future use

class User(TimestampMixin, SQLModel, table=True):
    # ... existing fields ...
    
    # Add role field
    role: UserRole = Field(
        default=UserRole.PATIENT,
        nullable=False,
        index=True,
        description="User role for access control"
    )
    
    # Security fields
    failed_login_attempts: int = Field(
        default=0,
        nullable=False,
        description="Failed login attempt counter"
    )
    
    locked_until: Optional[datetime] = Field(
        default=None,
        description="Account lock expiration time"
    )
    
    mfa_enabled: bool = Field(
        default=False,
        nullable=False,
        description="Multi-factor authentication status"
    )
    
    mfa_secret: Optional[str] = Field(
        default=None,
        max_length=32,
        description="MFA secret key (encrypted)"
    )
```

#### **Day 3: JWT Service Layer**

```python
# File: backend/app/modules/auth/services/jwt_service.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from fastapi import Request, Response

from ..models.user import User, UserRole
from ..models.refresh_token import RefreshToken
from ..models.security_audit import SecurityAuditLog
from ...core.security import jwt_handler, get_password_hash, verify_password
from ...core.config import settings

class SkinAidJWTService:
    """Healthcare-compliant JWT service for SkinAid"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.jwt_handler = jwt_handler
    
    async def authenticate_user(self, email: str, password: str, request: Request) -> Optional[User]:
        """Authenticate user with security logging"""
        
        # Get user
        result = await self.db.execute(
            select(User).where(User.email.ilike(email))
        )
        user = result.scalar_one_or_none()
        
        # Log authentication attempt
        await self._log_auth_event(
            event_type="login_attempt",
            user_email=email,
            user_id=user.id if user else None,
            success=False,  # Will update if successful
            request=request
        )
        
        if not user:
            return None
        
        # Check account lock
        if user.locked_until and user.locked_until > datetime.utcnow():
            await self._log_auth_event(
                event_type="login_blocked",
                user_id=user.id,
                user_email=email,
                success=False,
                request=request,
                details={"reason": "account_locked"}
            )
            return None
        
        # Verify password
        if not verify_password(password, user.hashed_password):
            # Increment failed attempts
            user.failed_login_attempts += 1
            
            # Lock account after 5 failed attempts
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                await self._log_auth_event(
                    event_type="account_locked",
                    user_id=user.id,
                    user_email=email,
                    success=False,
                    request=request,
                    details={"failed_attempts": user.failed_login_attempts}
                )
            
            await self.db.commit()
            return None
        
        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.utcnow()
        
        await self.db.commit()
        
        # Log successful authentication
        await self._log_auth_event(
            event_type="login_success",
            user_id=user.id,
            user_email=email,
            success=True,
            request=request
        )
        
        return user
    
    async def create_token_pair(self, user: User, request: Request, response: Response) -> Dict[str, Any]:
        """Create access and refresh token pair with secure cookie"""
        
        # Create access token
        access_token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
            "role": user.role.value,
            "permissions": self._get_user_permissions(user.role)
        }
        
        access_token = self.jwt_handler.create_access_token(access_token_data)
        
        # Create refresh token
        refresh_token = self.jwt_handler.create_refresh_token()
        device_fingerprint = self.jwt_handler.create_device_fingerprint(
            request.headers.get("user-agent", ""),
            request.client.host
        )
        
        # Store refresh token in database
        refresh_token_record = RefreshToken(
            token=refresh_token,
            user_id=user.id,
            expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
            device_fingerprint=device_fingerprint,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", "")[:500]
        )
        
        self.db.add(refresh_token_record)
        await self.db.commit()
        
        # Set refresh token in secure cookie
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
            httponly=True,
            secure=settings.COOKIE_SECURE,
            samesite=settings.COOKIE_SAMESITE,
            domain=settings.COOKIE_DOMAIN,
            path="/api/v1/auth"
        )
        
        # Log token creation
        await self._log_auth_event(
            event_type="token_created",
            user_id=user.id,
            success=True,
            request=request,
            details={"token_type": "access_refresh_pair"}
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "username": user.username,
                "role": user.role.value
            }
        }
    
    def _get_user_permissions(self, role: UserRole) -> list[str]:
        """Get permissions based on user role"""
        permission_map = {
            UserRole.PATIENT: [
                "wound_image:upload:own",
                "wound_image:view:own",
                "wound_image:delete:own",
                "analysis:view:own",
                "analysis:request:own",
                "profile:read:own",
                "profile:update:own"
            ],
            UserRole.PROFESSIONAL: [
                "patient:view:consented",
                "wound_image:view:consented",
                "analysis:view:consented",
                "analysis:override:professional",
                "medical_advice:provide",
                "report:generate:patient"
            ],
            UserRole.ADMIN: [
                "user:create:any",
                "user:read:any",
                "user:update:any",
                "user:delete:any",
                "system:monitor:all",
                "audit:view:all",
                "compliance:report:generate"
            ]
        }
        
        base_permissions = permission_map.get(role, [])
        
        # Professionals inherit patient permissions
        if role == UserRole.PROFESSIONAL:
            base_permissions.extend(permission_map[UserRole.PATIENT])
        
        # Admins inherit all permissions
        elif role == UserRole.ADMIN:
            for perm_list in permission_map.values():
                base_permissions.extend(perm_list)
        
        return list(set(base_permissions))  # Remove duplicates
    
    async def _log_auth_event(self, event_type: str, success: bool, request: Request, 
                             user_id: Optional[uuid.UUID] = None, 
                             user_email: Optional[str] = None,
                             details: Optional[Dict[str, Any]] = None):
        """Log authentication event for HIPAA compliance"""
        
        audit_log = SecurityAuditLog(
            event_type=event_type,
            event_category="auth",
            user_id=user_id,
            user_email=user_email,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", "")[:500],
            success=success,
            details=details or {},
            session_id=request.headers.get("x-session-id"),
            request_id=request.headers.get("x-request-id")
        )
        
        self.db.add(audit_log)
        await self.db.commit()
```

---

## 🎯 **PHASE 1 IMPLEMENTATION SUMMARY**

### **✅ Completed Infrastructure:**
- **JWT Configuration**: Healthcare-compliant settings
- **Security Handler**: Token creation and validation
- **Database Models**: Refresh tokens and audit logging
- **JWT Service**: Authentication with security logging
- **Role-Based Permissions**: Healthcare-specific roles

### **🔧 Key Features Implemented:**
- **Short Access Tokens**: 15-minute expiry for security
- **Secure Refresh Tokens**: httpOnly cookies with device fingerprinting
- **Account Lockout**: Protection against brute force attacks
- **Comprehensive Audit**: HIPAA-compliant logging
- **Role-Based Access**: Patient, Professional, Admin roles

### **📋 Next Steps (Phase 2):**
- Implement FastAPI dependencies for authentication
- Create protected route endpoints
- Add MFA for healthcare professionals
- Implement token refresh and rotation
- Add comprehensive testing

**Phase 1 provides the secure foundation for SkinAid's JWT authentication system with healthcare compliance built-in!** 🏥
