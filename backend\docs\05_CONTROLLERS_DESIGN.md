# 🎮 CONTROLLERS DESIGN - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn cách suy luận và thiết kế controller layer từ API requirements

---

## 🎯 **QUY TRÌNH THIẾT KẾ CONTROLLERS**

### **Bước 1: Controller Responsibilities Analysis - Phân tích trách nhiệm**

#### **🤔 Câu hỏi cốt lõi:**
```
1. Controller nên làm gì và KHÔNG nên làm gì?
2. Làm sao orchestrate multiple services?
3. Error handling strategy như thế nào?
4. Response formatting patterns?
5. HTTP status code mapping?
```

#### **Suy luận Controller Pattern:**
```python
# ✅ Controller SHOULD do:
"""
1. HTTP Request/Response handling
2. Input validation (format, not business rules)
3. Service orchestration
4. Exception handling and mapping
5. Response formatting
6. HTTP status code setting
7. Authentication/authorization checks
"""

# ❌ Controller SHOULD NOT do:
"""
1. Business logic (→ Service layer)
2. Database operations (→ Service layer)
3. Complex data transformations (→ Service layer)
4. External API calls (→ Service layer)
5. File I/O operations (→ Service layer)
"""

# Thin Controller Pattern
class AuthController:
    """
    Suy luận: Controller chỉ là orchestrator
    - Nhận HTTP request
    - Gọi appropriate services
    - Handle exceptions
    - Format HTTP response
    """
    
    def __init__(self, db: AsyncSession):
        # Inject services
        self.auth_service = AuthService(db)
        self.user_service = UserService(db)
        self.email_service = EmailService()
```

### **Bước 2: Method Design Pattern - Mẫu thiết kế phương thức**

#### **🧠 Suy luận standard controller method structure:**
```python
class AuthController:
    async def register(self, user_data: UserCreate) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        """
        Suy luận standard flow:
        1. Try block: Happy path execution
        2. Specific exception handling: Known business errors
        3. Generic exception handling: Unexpected errors
        4. Always return consistent response format
        """
        
        try:
            # Step 1: Service orchestration (happy path)
            user = await self.auth_service.create_user(user_data)
            user_with_profile = await self.user_service.get_user_with_profile(user.id)
            
            # Step 2: Response transformation
            user_response = self._create_user_response(user_with_profile)
            
            # Step 3: Success response
            return SuccessResponse(
                message=Messages.REGISTER_SUCCESS,
                data=user_response
            )
        
        # Step 4: Specific error handling
        except EmailAlreadyExistsError as e:
            return self._handle_email_exists_error(e, user_data.email)
        
        except WeakPasswordError as e:
            return self._handle_weak_password_error(e)
        
        except ValidationException as e:
            return self._handle_validation_error(e)
        
        # Step 5: Generic error handling
        except Exception as e:
            return self._handle_unexpected_error(e, "register")
    
    def _create_user_response(self, user: User) -> UserResponse:
        """
        Suy luận: Extract response creation to separate method
        - Reusable across multiple controller methods
        - Easier to test transformation logic
        - Single responsibility principle
        """
        return UserResponse(
            user_id=user.id,
            username=user.username,
            email=user.email,
            created_at=user.created_at,
            # Safe profile access
            full_name=user.profile.full_name if user.profile else None,
            phone=user.profile.phone if user.profile else None,
            avatar_url=user.profile.avatar_url if user.profile else None
        )
```

### **Bước 3: Error Handling Strategy - Chiến lược xử lý lỗi**

#### **🤔 Suy luận error response mapping:**
```python
class AuthController:
    """
    Suy luận error handling principles:
    1. Specific errors get specific responses
    2. User-friendly messages
    3. Appropriate HTTP status codes
    4. Consistent error response format
    5. Security considerations (don't leak info)
    """
    
    def _handle_email_exists_error(self, error: EmailAlreadyExistsError, email: str) -> ErrorResponse:
        """
        Suy luận: Email exists error handling
        - HTTP 409 Conflict (resource already exists)
        - User-friendly message with guidance
        - Include context for frontend (highlight email field)
        """
        return ErrorResponse(
            message=error.message,
            error_code=error.error_code,
            error_details={
                "field": "email",
                "value": email,
                "suggestion": "Try logging in instead or use a different email"
            }
        ), 409
    
    def _handle_weak_password_error(self, error: WeakPasswordError) -> ErrorResponse:
        """
        Suy luận: Password validation error
        - HTTP 400 Bad Request (client input error)
        - Educational message with requirements
        - Help user create better password
        """
        return ErrorResponse(
            message=error.message,
            error_code=error.error_code,
            error_details={
                "field": "password",
                "requirements": error.context.get("requirements", []),
                "tips": [
                    "Use a mix of uppercase and lowercase letters",
                    "Include numbers and special characters",
                    "Make it at least 8 characters long"
                ]
            }
        ), 400
    
    def _handle_validation_error(self, error: ValidationException) -> ErrorResponse:
        """
        Suy luận: General validation error
        - HTTP 400 Bad Request
        - Field-specific error details
        - Multiple errors per field support
        """
        field_errors = {}
        for field_error in error.context.get("errors", []):
            field = field_error.get("field", "unknown")
            message = field_error.get("message", "Invalid value")
            
            if field not in field_errors:
                field_errors[field] = []
            field_errors[field].append(message)
        
        return ErrorResponse(
            message="Please fix the following validation errors",
            error_code=error.error_code,
            error_details={"field_errors": field_errors}
        ), 400
    
    def _handle_unexpected_error(self, error: Exception, operation: str) -> ErrorResponse:
        """
        Suy luận: Unexpected error handling
        - HTTP 500 Internal Server Error
        - Log detailed error for debugging
        - Generic user message (don't expose internals)
        - Include operation context for debugging
        """
        error_id = str(uuid.uuid4())  # Unique error ID for tracking
        
        logger.error(
            f"Unexpected error in {operation}",
            extra={
                "error_id": error_id,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "operation": operation,
                "traceback": traceback.format_exc()
            }
        )
        
        return ErrorResponse(
            message="We're experiencing technical difficulties. Please try again later.",
            error_code="INTERNAL_ERROR",
            error_details={"error_id": error_id}  # For support reference
        ), 500
```

### **Bước 4: Service Orchestration - Điều phối dịch vụ**

#### **🧠 Suy luận multi-service coordination:**
```python
class AuthController:
    async def register(self, user_data: UserCreate) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        """
        Suy luận: Complex operation requiring multiple services
        1. User creation (AuthService)
        2. Profile initialization (UserService)
        3. Welcome email (EmailService)
        4. Analytics tracking (AnalyticsService)
        
        Question: What if one service fails?
        Answer: Depends on criticality
        """
        
        try:
            # Critical operations (must succeed)
            user = await self.auth_service.create_user(user_data)
            profile = await self.user_service.create_initial_profile(user.id)
            
            # Non-critical operations (can fail gracefully)
            background_tasks = []
            
            # Welcome email - nice to have
            try:
                await self.email_service.send_welcome_email(user.email, user.username)
            except EmailServiceException as e:
                logger.warning(f"Failed to send welcome email: {str(e)}")
                background_tasks.append(("welcome_email", user.id))
            
            # Analytics tracking - nice to have
            try:
                await self.analytics_service.track_user_registration(user.id)
            except AnalyticsException as e:
                logger.warning(f"Failed to track registration: {str(e)}")
                background_tasks.append(("track_registration", user.id))
            
            # Queue failed background tasks for retry
            if background_tasks:
                await self.task_queue.enqueue_batch(background_tasks)
            
            # Return success response
            user_response = self._create_user_response_with_profile(user, profile)
            return SuccessResponse(
                message=Messages.REGISTER_SUCCESS,
                data=user_response
            )
            
        except (EmailAlreadyExistsError, WeakPasswordError, ValidationException):
            # Re-raise business exceptions for specific handling
            raise
        except Exception as e:
            # Handle unexpected errors
            return self._handle_unexpected_error(e, "register")
    
    async def login(self, login_data: UserLogin) -> Union[SuccessResponse[LoginResponse], ErrorResponse]:
        """
        Suy luận: Login operation orchestration
        1. Authenticate user (AuthService)
        2. Generate token (TokenService)
        3. Update last login (UserService)
        4. Log security event (SecurityService)
        """
        
        try:
            # Step 1: Authenticate
            user = await self.auth_service.authenticate_user(
                login_data.email, 
                login_data.password
            )
            
            # Step 2: Generate token
            token_data = TokenPayload(
                user_id=user.id,
                email=user.email,
                username=user.username
            )
            access_token = await self.token_service.create_access_token(token_data)
            
            # Step 3: Update last login (non-critical)
            try:
                await self.user_service.update_last_login(user.id)
            except Exception as e:
                logger.warning(f"Failed to update last login: {str(e)}")
            
            # Step 4: Security logging (non-critical)
            try:
                await self.security_service.log_successful_login(user.id, request.client.host)
            except Exception as e:
                logger.warning(f"Failed to log security event: {str(e)}")
            
            # Response
            login_response = LoginResponse(
                access_token=access_token,
                token_type="bearer",
                expires_in=self.token_service.get_token_expiry_seconds(),
                user=self._create_user_response(user)
            )
            
            return SuccessResponse(
                message=Messages.LOGIN_SUCCESS,
                data=login_response
            )
            
        except (InvalidCredentialsError, AccountDeactivatedError):
            raise
        except Exception as e:
            return self._handle_unexpected_error(e, "login")
```

### **Bước 5: Response Transformation - Chuyển đổi phản hồi**

#### **🤔 Suy luận response formatting strategy:**
```python
class AuthController:
    """
    Suy luận response transformation principles:
    1. Consistent response format across all endpoints
    2. Include only necessary data (security)
    3. Transform domain models to API schemas
    4. Handle optional/missing data gracefully
    """
    
    def _create_user_response(self, user: User) -> UserResponse:
        """
        Suy luận: Basic user response transformation
        - Include public user data
        - Exclude sensitive information
        - Handle missing profile gracefully
        """
        return UserResponse(
            user_id=user.id,
            username=user.username,
            email=user.email,
            created_at=user.created_at,
            # Profile data (may be None)
            full_name=user.profile.full_name if user.profile else None,
            phone=user.profile.phone if user.profile else None,
            avatar_url=user.profile.avatar_url if user.profile else None
        )
    
    def _create_detailed_user_response(self, user: User) -> DetailedUserResponse:
        """
        Suy luận: Detailed response for profile endpoints
        - Include more profile information
        - Add computed fields
        - Include user preferences
        """
        return DetailedUserResponse(
            user_id=user.id,
            username=user.username,
            email=user.email,
            created_at=user.created_at,
            last_login=user.last_login,
            # Profile information
            profile=UserProfileResponse(
                full_name=user.profile.full_name if user.profile else None,
                phone=user.profile.phone if user.profile else None,
                date_of_birth=user.profile.date_of_birth if user.profile else None,
                gender=user.profile.gender if user.profile else None,
                address=user.profile.address if user.profile else None,
                avatar_url=user.profile.avatar_url if user.profile else None
            ) if user.profile else None,
            # Computed fields
            account_age_days=(datetime.utcnow() - user.created_at).days,
            is_profile_complete=self._is_profile_complete(user.profile)
        )
    
    def _is_profile_complete(self, profile: Optional[UserProfile]) -> bool:
        """
        Suy luận: Business logic for profile completeness
        - Define what makes a profile "complete"
        - Used for UI prompts and onboarding
        """
        if not profile:
            return False
        
        required_fields = [
            profile.full_name,
            profile.phone,
            profile.date_of_birth
        ]
        
        return all(field is not None for field in required_fields)
```

### **Bước 6: HTTP Status Code Strategy - Chiến lược mã trạng thái**

#### **🧠 Suy luận status code mapping:**
```python
class AuthController:
    """
    Suy luận HTTP status codes:
    - Follow HTTP semantics
    - Be consistent across endpoints
    - Consider client behavior
    """
    
    def _get_status_code_for_error(self, error: Exception) -> int:
        """
        Suy luận status code mapping:
        
        2xx Success:
        200 OK - Successful GET, PUT, DELETE
        201 Created - Successful POST (resource created)
        204 No Content - Successful DELETE (no response body)
        
        4xx Client Errors:
        400 Bad Request - Validation errors, malformed requests
        401 Unauthorized - Authentication required
        403 Forbidden - Authenticated but not authorized
        404 Not Found - Resource doesn't exist
        409 Conflict - Resource already exists
        422 Unprocessable Entity - Semantic errors
        
        5xx Server Errors:
        500 Internal Server Error - Unexpected server errors
        502 Bad Gateway - External service errors
        503 Service Unavailable - Temporary unavailability
        """
        
        status_mapping = {
            # Authentication errors
            InvalidCredentialsError: 401,
            TokenExpiredError: 401,
            AccountDeactivatedError: 403,
            InsufficientPermissionsError: 403,
            
            # Validation errors
            ValidationException: 400,
            WeakPasswordError: 400,
            InvalidEmailFormatError: 400,
            
            # Business rule violations
            EmailAlreadyExistsError: 409,
            UsernameAlreadyExistsError: 409,
            
            # Resource errors
            UserNotFoundError: 404,
            ProfileNotFoundError: 404,
            
            # System errors
            DatabaseConnectionError: 500,
            ExternalServiceException: 502,
            FileStorageError: 500,
        }
        
        return status_mapping.get(type(error), 500)
    
    async def register(self, user_data: UserCreate):
        try:
            user = await self.auth_service.create_user(user_data)
            user_response = self._create_user_response(user)
            
            return SuccessResponse(
                message=Messages.REGISTER_SUCCESS,
                data=user_response
            ), 201  # Created - new resource
            
        except Exception as e:
            error_response = self._create_error_response(e)
            status_code = self._get_status_code_for_error(e)
            return error_response, status_code
```

### **Bước 7: Security Considerations - Cân nhắc bảo mật**

#### **🤔 Suy luận security patterns:**
```python
class AuthController:
    """
    Suy luận security considerations:
    1. Input sanitization
    2. Rate limiting
    3. Audit logging
    4. Error message security
    5. Data exposure prevention
    """
    
    async def login(self, login_data: UserLogin, request: Request):
        """Security-enhanced login"""
        
        # Rate limiting check
        client_ip = request.client.host
        if await self.rate_limiter.is_rate_limited("login", client_ip):
            await self.security_service.log_rate_limit_exceeded(client_ip, "login")
            return ErrorResponse(
                message="Too many login attempts. Please try again later.",
                error_code="RATE_LIMIT_EXCEEDED"
            ), 429
        
        try:
            # Authenticate user
            user = await self.auth_service.authenticate_user(
                login_data.email.lower().strip(),  # Normalize email
                login_data.password
            )
            
            # Security logging
            await self.security_service.log_successful_login(
                user_id=user.id,
                ip_address=client_ip,
                user_agent=request.headers.get("user-agent", "unknown")
            )
            
            # Generate token
            token = await self.token_service.create_access_token(user)
            
            return SuccessResponse(
                message=Messages.LOGIN_SUCCESS,
                data={"access_token": token, "user": self._create_user_response(user)}
            ), 200
            
        except InvalidCredentialsError as e:
            # Security: Don't specify if email or password is wrong
            await self.security_service.log_failed_login(
                email=login_data.email,
                ip_address=client_ip,
                reason="invalid_credentials"
            )
            
            return ErrorResponse(
                message=e.message,  # Generic message
                error_code=e.error_code
            ), 401
            
        except AccountDeactivatedError as e:
            # Security: Log suspicious activity
            await self.security_service.log_deactivated_account_access(
                email=login_data.email,
                ip_address=client_ip
            )
            
            return ErrorResponse(
                message=e.message,
                error_code=e.error_code
            ), 403
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Keep controllers thin (orchestration only)
- Use consistent error handling patterns
- Return appropriate HTTP status codes
- Transform domain models to API schemas
- Implement comprehensive logging
- Handle security considerations
- Use dependency injection for services
- Write unit tests for controller logic

### **❌ DON'T:**
- Put business logic in controllers
- Expose sensitive data in responses
- Use generic error messages
- Ignore security considerations
- Skip input sanitization
- Return inconsistent response formats
- Hardcode service dependencies

### **🔍 REVIEW CHECKLIST:**
- [ ] Controllers follow thin controller pattern?
- [ ] Error handling is comprehensive and consistent?
- [ ] HTTP status codes are appropriate?
- [ ] Response formats are consistent?
- [ ] Security considerations addressed?
- [ ] Services properly orchestrated?
- [ ] Logging provides adequate debugging info?

**Remember: Controllers are the gateway between HTTP and your business logic!** 🎮
