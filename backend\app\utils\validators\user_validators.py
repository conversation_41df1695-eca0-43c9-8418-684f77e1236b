from typing import Optional 
from app.utils.constants.regex_patterns import PHONE_PATTERN 
import re 

def validate_phone_number(phone_number: str)-> Optional[str]: 

    if phone_number is None: 
        return None 
    
    if not re.match(PHONE_PATTERN, phone_number): 
        return "Invalid phone number format"
    
    return None 

def validate_full_name(full_name: str)-> Optional[str]:

    if full_name is None:
        return None

    if len(full_name) < 2:
        return "Full name must be at least 2 characters"

    if len(full_name) > 255:
        return "Full name must not exceed 255 characters"

    return None

def validate_username(username: str) -> Optional[str]:
    """Validate username format and length"""

    if username is None:
        return "Username is required"

    if len(username) < 2:
        return "Username must be at least 2 characters"

    if len(username) > 50:
        return "Username must not exceed 50 characters"

    # Check for valid characters (letters, numbers, underscore, hyphen)
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return "Username can only contain letters, numbers, underscore, and hyphen"

    return None