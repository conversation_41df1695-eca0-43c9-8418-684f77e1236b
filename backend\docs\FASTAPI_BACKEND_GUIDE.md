# 📚 HƯỚNG DẪN TOÀN DIỆN: FASTAPI BACKEND CHO NGƯỜI MỚI BẮT ĐẦU

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn chi tiết cách xây dựng backend FastAPI từ A-Z cho dự án SkinAid  
**Đối tượng**: <PERSON><PERSON> viên/học sinh chưa biết gì về FastAPI  
**Ngôn ngữ**: <PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> th<PERSON><PERSON> đ<PERSON>, d<PERSON> hiểu

---

## 🎯 **MỤC LỤC**

1. [Tổng quan kiến trúc](#1-tổng-quan-kiến-trúc)
2. [Database Models - Nền tảng dữ liệu](#2-database-models---nền-tảng-dữ-liệu)
3. [Schemas - Định dạng dữ liệu API](#3-schemas---định-dạng-dữ-liệu-api)
4. [Exceptions - Xử lý lỗi chuyên nghiệp](#4-exceptions---xử-lý-lỗi-chuyên-nghiệp)
5. [Services - Logic nghiệp vụ](#5-services---logic-nghiệp-vụ)
6. [Controllers - Đ<PERSON><PERSON><PERSON> khiển luồng xử lý](#6-controllers---điều-khiển-luồng-xử-lý)
7. [Routes - API Endpoints](#7-routes---api-endpoints)
8. [Authentication & JWT](#8-authentication--jwt)
9. [Database Migration](#9-database-migration)
10. [Testing & Debugging](#10-testing--debugging)

---

## 1. **TỔNG QUAN KIẾN TRÚC**

### **🏗️ Kiến trúc Layered Architecture (Kiến trúc phân lớp)**

Hãy tưởng tượng backend như một tòa nhà 5 tầng:

```
🌐 Tầng 5: Routes (API Endpoints)     - Cửa hàng (khách hàng giao tiếp)
🎮 Tầng 4: Controllers                - Quản lý (điều phối công việc)  
🔧 Tầng 3: Services                   - Nhân viên (xử lý logic)
📊 Tầng 2: Models (Database)          - Kho hàng (lưu trữ dữ liệu)
🏠 Tầng 1: Schemas (Data Validation)  - Quy tắc (kiểm tra dữ liệu)
```

### **🔄 Luồng xử lý request:**

```
Client Request → Routes → Controllers → Services → Database
                   ↓         ↓          ↓         ↓
                Nhận      Điều phối   Xử lý     Lưu trữ
                request   công việc   logic     dữ liệu
```

### **📁 Cấu trúc thư mục:**

```
backend/
├── app/
│   ├── main.py                 # 🚀 Entry point - Khởi động server
│   ├── core/                   # ⚙️ Cấu hình cốt lõi
│   │   ├── config.py          # 📋 Settings (database, JWT, etc.)
│   │   ├── database.py        # 🗄️ Kết nối database
│   │   └── security.py        # 🔐 Bảo mật (hash password, JWT)
│   ├── api/v1/                # 🌐 API version 1
│   │   ├── api.py            # 📡 Router chính
│   │   └── deps.py           # 🔗 Dependencies (get_db, etc.)
│   ├── modules/auth/          # 🔐 Module xác thực
│   │   ├── models/           # 📊 Database models
│   │   ├── schemas/          # 📋 API schemas
│   │   ├── services/         # 🔧 Business logic
│   │   ├── controllers/      # 🎮 Request handlers
│   │   └── routes/           # 🌐 API endpoints
│   ├── shared/               # 🤝 Code dùng chung
│   │   ├── models/          # 📊 Base models
│   │   └── schemas/         # 📋 Response schemas
│   └── utils/               # 🛠️ Tiện ích
│       ├── exceptions/      # ❌ Custom exceptions
│       ├── validators/      # ✅ Validation functions
│       └── constants/       # 📝 Hằng số
```

### **🤔 Tại sao phân chia như vậy?**

1. **Separation of Concerns** (Tách biệt trách nhiệm):
   - Mỗi layer có 1 nhiệm vụ cụ thể
   - Dễ debug khi có lỗi
   - Dễ test từng phần riêng biệt

2. **Maintainability** (Dễ bảo trì):
   - Thay đổi database không ảnh hưởng API
   - Thay đổi logic không ảnh hưởng UI
   - Code sạch, dễ đọc

3. **Scalability** (Mở rộng dễ dàng):
   - Thêm feature mới không phá code cũ
   - Có thể tách thành microservices sau này
   - Team nhiều người làm song song

---

## 2. **DATABASE MODELS - NỀN TẢNG DỮ LIỆU**

### **🎯 Models là gì?**

Models là **"bản thiết kế"** của database tables. Giống như bản vẽ kiến trúc của ngôi nhà, nó định nghĩa:
- Table có những cột nào
- Kiểu dữ liệu của mỗi cột
- Ràng buộc (unique, not null, foreign key)
- Mối quan hệ giữa các tables

### **🧠 CÁCH SUY LUẬN VÀ THIẾT KẾ DATABASE MODELS**

#### **Bước 1: Phân tích Domain và Entities**

**Ví dụ Domain**: SkinAid - Ứng dụng phân tích vết thương da

**Suy luận Entities:**
```python
# 🤔 Câu hỏi: Hệ thống cần lưu trữ thông tin gì?

# Phân tích từ business requirements:
"""
1. User đăng ký, đăng nhập → Cần User entity
2. User có thông tin cá nhân → Cần UserProfile entity
3. User upload ảnh vết thương → Cần WoundImage entity
4. AI phân tích vết thương → Cần WoundAnalysis entity
5. System đưa ra khuyến nghị → Cần Recommendation entity
"""

# Entities mapping:
entities = {
    "User": "Thông tin đăng nhập, authentication",
    "UserProfile": "Thông tin cá nhân, demographics",
    "WoundImage": "Ảnh vết thương được upload",
    "WoundAnalysis": "Kết quả phân tích AI",
    "Recommendation": "Khuyến nghị điều trị"
}
```

#### **Bước 2: Thiết kế Entity Attributes**

**Cách suy luận attributes cho User entity:**
```python
# 🤔 User entity cần những thông tin gì?

class User(SQLModel, table=True):
    """
    Suy luận từ authentication requirements:

    1. Cần unique identifier → id (UUID cho security)
    2. Cần login credential → email (unique)
    3. Cần display name → username (unique, user-friendly)
    4. Cần authentication → hashed_password (không lưu plain text)
    5. Cần account management → is_active, is_verified
    6. Cần audit trail → created_at, updated_at
    """

    # Primary Key
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
        # 🤔 Tại sao UUID thay vì auto-increment integer?
        # → UUID không đoán được, bảo mật hơn
        # → Distributed system friendly
        # → Không leak business info (số lượng users)
    )

    # Authentication fields
    username: str = Field(
        unique=True,           # 🤔 Tại sao unique? → Không được trùng display name
        nullable=False,        # 🤔 Tại sao not null? → Bắt buộc phải có
        index=True,           # 🤔 Tại sao index? → Tìm kiếm nhanh khi login
        max_length=50         # 🤔 Tại sao 50? → Đủ dài cho tên, không quá dài cho UI
    )

    email: str = Field(
        unique=True,          # 🤔 Tại sao unique? → Một email chỉ một tài khoản
        nullable=False,       # 🤔 Tại sao not null? → Cần để login và contact
        index=True           # 🤔 Tại sao index? → Login by email cần tìm nhanh
    )

    hashed_password: str = Field(
        nullable=False       # 🤔 Tại sao not null? → Không có password không login được
        # 🤔 Tại sao không có max_length? → Hash length cố định (bcrypt = 60 chars)
    )

    # Account status
    is_active: bool = Field(
        default=True,        # 🤔 Tại sao default True? → User mới tạo nên active
        nullable=False       # 🤔 Tại sao not null? → Phải biết rõ trạng thái
    )

    is_verified: bool = Field(
        default=False,       # 🤔 Tại sao default False? → Cần verify email trước
        nullable=False       # 🤔 Tại sao not null? → Phải biết rõ đã verify chưa
    )
```

#### **Bước 3: Thiết kế Entity Relationships**

**Cách suy luận relationships:**
```python
# 🤔 Mối quan hệ giữa User và UserProfile là gì?

"""
Phân tích business logic:
- Một User có một Profile → One-to-One
- Profile không thể tồn tại mà không có User → Dependent
- Xóa User thì xóa Profile → Cascade delete
"""

class User(SQLModel, table=True):
    # ... other fields ...

    profile: Optional["UserProfile"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={
            "uselist": False,        # 🤔 Tại sao False? → One-to-One relationship
            "cascade": "all, delete-orphan"  # 🤔 Tại sao cascade? → Xóa User → xóa Profile
        }
    )

class UserProfile(SQLModel, table=True):
    # Foreign Key
    user_id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID,
            ForeignKey("users.id"),  # 🤔 Reference đến users.id
            nullable=False,          # 🤔 Profile phải thuộc về User
            unique=True,            # 🤔 Một User chỉ có một Profile
            index=True              # 🤔 Tìm profile by user_id nhanh
        )
    )

    user: Optional["User"] = Relationship(back_populates="profile")
```

#### **Bước 4: Thiết kế Data Types**

**Cách suy luận data types:**
```python
# 🤔 Field nào nên dùng data type gì?

class UserProfile(SQLModel, table=True):
    full_name: Optional[str] = Field(
        default=None,        # 🤔 Tại sao Optional? → Không bắt buộc khi đăng ký
        max_length=255       # 🤔 Tại sao 255? → Đủ cho tên dài, chuẩn VARCHAR
    )

    phone: Optional[str] = Field(
        default=None,
        max_length=20        # 🤔 Tại sao 20? → Đủ cho international format +***********
    )

    date_of_birth: Optional[date] = Field(
        default=None         # 🤔 Tại sao date không phải datetime? → Chỉ cần ngày, không cần giờ
    )

    gender: Optional[GenderEnum] = Field(
        default=None         # 🤔 Tại sao Enum? → Giới hạn values, data consistency
    )

    address: Optional[str] = Field(
        default=None,
        max_length=255       # 🤔 Tại sao 255? → Đủ cho địa chỉ dài
    )

    avatar_url: Optional[str] = Field(
        default=None,
        max_length=500       # 🤔 Tại sao 500? → URL có thể dài, CDN URLs
    )

# 🤔 Tại sao tạo GenderEnum?
class GenderEnum(str, enum.Enum):
    """
    Suy luận: Gender có bao nhiêu values hợp lệ?
    - male, female (traditional)
    - other (inclusive)
    - Không nên free text → data inconsistency
    """
    male = "male"
    female = "female"
    other = "other"
```

#### **Bước 5: Thiết kế Indexes và Performance**

**Cách suy luận indexing strategy:**
```python
# 🤔 Query nào sẽ chạy thường xuyên?

"""
Phân tích query patterns:
1. Login by email → Cần index trên email
2. Find user by username → Cần index trên username
3. Get profile by user_id → Cần index trên user_id
4. Search users by name → Cần index trên full_name
"""

class User(SQLModel, table=True):
    email: str = Field(unique=True, index=True)      # Login query
    username: str = Field(unique=True, index=True)   # Profile lookup

class UserProfile(SQLModel, table=True):
    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), index=True)  # Join query
    )
    full_name: Optional[str] = Field(max_length=255)  # Search query (index tạo sau)

# Tạo additional indexes
"""
CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);
CREATE INDEX idx_users_created_at ON users(created_at);  -- For analytics
"""
```

#### **Bước 6: Thiết kế Audit Trail và Metadata**

**Cách suy luận audit requirements:**
```python
# 🤔 Cần track thông tin gì để debug và audit?

class TimestampMixin(SQLModel):
    """
    Suy luận audit requirements:
    1. Khi nào record được tạo? → created_at
    2. Khi nào record được update lần cuối? → updated_at
    3. Ai tạo/update record? → created_by, updated_by (future)
    """
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        nullable=False
        # 🤔 Tại sao UTC? → Consistent timezone, international app
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        nullable=False,
        sa_column_kwargs={"onupdate": lambda: datetime.now(timezone.utc)}
        # 🤔 onupdate? → Tự động update khi record thay đổi
    )

# Tất cả models inherit từ TimestampMixin
class User(TimestampMixin, SQLModel, table=True):
    # ... fields ...
    pass

class UserProfile(TimestampMixin, SQLModel, table=True):
    # ... fields ...
    pass
```

### **📊 Ví dụ thực tế: User Model**

```python
# backend/app/modules/auth/models/user.py

from sqlmodel import SQLModel, Field, Column, Relationship 
import uuid 
import sqlalchemy.dialects.postgresql as pg 
from typing import Optional, TYPE_CHECKING
from app.shared.models.basemodel import TimestampMixin

# TYPE_CHECKING: Tránh circular import
if TYPE_CHECKING: 
    from app.modules.auth.models.user_profile import UserProfile

class User(TimestampMixin, SQLModel, table=True):
    """
    Model User - Thông tin đăng nhập cơ bản
    
    Giải thích từng phần:
    - TimestampMixin: Tự động thêm created_at, updated_at
    - SQLModel: Kết hợp SQLAlchemy + Pydantic
    - table=True: Tạo table trong database
    """
    
    __tablename__ = "users"  # Tên table trong database
    
    # Primary Key - Khóa chính
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )
    # Giải thích: 
    # - uuid.UUID: Kiểu dữ liệu UUID (unique identifier)
    # - primary_key=True: Đây là khóa chính
    # - default=uuid.uuid4: Tự động tạo UUID mới
    
    # Thông tin đăng nhập
    username: str = Field(
        unique=True,           # Không được trùng
        nullable=False,        # Bắt buộc phải có
        index=True,           # Tạo index để tìm kiếm nhanh
        max_length=50         # Tối đa 50 ký tự
    )
    
    email: str = Field(
        unique=True, 
        nullable=False, 
        index=True
    )
    
    hashed_password: str = Field(nullable=False)
    # Lưu ý: Không bao giờ lưu password gốc, chỉ lưu hash
    
    # Trạng thái tài khoản
    is_active: bool = Field(default=True, nullable=False)
    is_verified: bool = Field(default=False, nullable=False)
    
    # Relationship - Mối quan hệ với UserProfile
    profile: Optional["UserProfile"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={
            "uselist": False,        # 1-1 relationship
            "cascade": "all, delete-orphan"  # Xóa user → xóa profile
        }
    )
    
    def __repr__(self):
        """Hiển thị khi print(user)"""
        return f"<User {self.username}>"
    
    @property
    def display_name(self) -> str:
        """Tên hiển thị cho user"""
        return self.username
```

### **👤 UserProfile Model - Thông tin cá nhân**

```python
# backend/app/modules/auth/models/user_profile.py

class UserProfile(TimestampMixin, SQLModel, table=True):
    """
    Model UserProfile - Thông tin cá nhân chi tiết
    
    Tại sao tách riêng?
    - User: Thông tin đăng nhập (ít thay đổi)
    - UserProfile: Thông tin cá nhân (thường xuyên cập nhật)
    """
    
    __tablename__ = "user_profiles"
    
    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )
    
    # Foreign Key - Khóa ngoại liên kết với User
    user_id: uuid.UUID = Field(
        sa_column=Column(
            pg.UUID, 
            ForeignKey("users.id"),  # Liên kết với users.id
            nullable=False, 
            unique=True,            # 1 user chỉ có 1 profile
            index=True
        )
    )
    
    # Thông tin cá nhân
    full_name: Optional[str] = Field(
        default=None, 
        max_length=255, 
        description="Tên đầy đủ của user"
    )
    phone: Optional[str] = Field(default=None, max_length=20)
    date_of_birth: Optional[date] = Field(default=None)
    gender: Optional[GenderEnum] = Field(default=None)
    address: Optional[str] = Field(default=None, max_length=255)
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    
    # Relationship ngược lại với User
    user: Optional["User"] = Relationship(back_populates="profile")
    
    @property
    def age(self) -> Optional[int]:
        """Tính tuổi từ ngày sinh"""
        if not self.date_of_birth:
            return None
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )
```

### **🔗 Hiểu về Relationships**

```python
# Trong User model:
profile: Optional["UserProfile"] = Relationship(back_populates="user")

# Trong UserProfile model:  
user: Optional["User"] = Relationship(back_populates="profile")
```

**Giải thích:**
- `Relationship`: Tạo liên kết giữa 2 models
- `back_populates`: Tên attribute ở model kia
- `Optional`: Có thể None (user chưa tạo profile)

**Cách sử dụng:**
```python
# Lấy user và profile
user = await get_user_by_id(user_id)
print(user.username)                    # john_doe
print(user.profile.full_name)          # John Doe Smith
print(user.profile.age)                # 25

# Từ profile lấy user
profile = await get_profile_by_id(profile_id)
print(profile.user.email)              # <EMAIL>
```

### **🎯 Tại sao dùng SQLModel?**

**SQLModel = SQLAlchemy + Pydantic**

1. **SQLAlchemy**: ORM mạnh mẽ cho database
2. **Pydantic**: Validation và serialization
3. **SQLModel**: Kết hợp 2 cái trên, viết 1 lần dùng được nhiều nơi

**Lợi ích:**
- ✅ Type hints đầy đủ
- ✅ Auto validation
- ✅ Dễ convert sang JSON
- ✅ IDE support tốt
- ✅ Ít code hơn

---

## 3. **SCHEMAS - ĐỊNH DẠNG DỮ LIỆU API**

### **🎯 Schemas là gì?**

Schemas là **"hợp đồng"** giữa frontend và backend. Nó định nghĩa:
- Client gửi data gì lên server (Input)
- Server trả data gì về client (Output)
- Validation rules (email phải đúng format, password ít nhất 8 ký tự)

### **🧠 CÁCH SUY LUẬN VÀ THIẾT KẾ SCHEMAS**

#### **Bước 1: Phân tích API Requirements từ User Stories**

**Ví dụ User Story**: "Là user, tôi muốn đăng ký tài khoản để sử dụng app"

**Suy luận Input Schema:**
```python
# 🤔 Câu hỏi: User cần cung cấp thông tin gì để đăng ký?

# Phân tích từ business requirements:
# 1. Cần identifier unique → email hoặc username?
# 2. Cần authentication → password
# 3. Cần contact → email (có thể dùng để reset password)
# 4. Cần display name → username (hiển thị trong app)

# Kết luận: UserCreate schema cần:
class UserCreate(BaseModel):
    username: str    # Display name, unique identifier
    email: EmailStr  # Contact, login identifier
    password: str    # Authentication

    # 🤔 Tại sao không có full_name?
    # → Full name là thông tin cá nhân, không bắt buộc khi đăng ký
    # → Có thể cập nhật sau trong profile
```

**Suy luận Output Schema:**
```python
# 🤔 Câu hỏi: Sau khi đăng ký thành công, frontend cần gì?

# Phân tích frontend requirements:
# 1. Hiển thị "Chào mừng [username]" → cần username
# 2. Hiển thị email để confirm → cần email
# 3. Redirect đến profile page → cần user_id
# 4. Show registration time → cần created_at
# 5. Hiển thị avatar nếu có → cần avatar_url từ profile

# Kết luận: UserResponse schema cần:
class UserResponse(BaseModel):
    user_id: uuid.UUID     # Để redirect, API calls
    username: str          # Hiển thị welcome message
    email: EmailStr        # Hiển thị confirmation
    created_at: datetime   # Show "Member since..."

    # Profile info (optional - có thể None)
    full_name: Optional[str] = None    # Hiển thị tên thật nếu có
    phone: Optional[str] = None        # Contact info
    avatar_url: Optional[str] = None   # Profile picture
```

#### **Bước 2: Phân loại Schemas theo Data Flow**

**Cách suy luận phân loại:**
```python
# 🤔 Data flow trong hệ thống như thế nào?

"""
Client → Server (INPUT):
- UserCreate: Đăng ký user mới
- UserLogin: Đăng nhập
- UserUpdate: Cập nhật thông tin user
- UserProfileUpdate: Cập nhật profile

Server → Client (OUTPUT):
- UserResponse: Thông tin user trả về client
- UserProfileResponse: Thông tin profile
- ErrorResponse: Thông báo lỗi

Internal (PROCESSING):
- UserRead: Đọc full data từ DB (admin)
- TokenPayload: Data trong JWT token
"""

# Nguyên tắc đặt tên:
# [Entity][Action] hoặc [Entity][Purpose]
# - UserCreate = User + Create (action)
# - UserResponse = User + Response (purpose)
# - TokenPayload = Token + Payload (purpose)
```

#### **Bước 3: Thiết kế Validation Rules**

**Cách suy luận validation:**
```python
# 🤔 Username hợp lệ khi nào?

class UserCreate(BaseModel):
    username: str = Field(
        ...,                    # Required - tại sao? → Cần để hiển thị
        min_length=2,          # Tại sao 2? → Tên quá ngắn không có nghĩa
        max_length=50,         # Tại sao 50? → Đủ dài cho tên, không quá dài cho UI
        regex=r'^[a-zA-Z0-9_-]+$',  # Tại sao regex này? → Chỉ cho phép ký tự an toàn cho URL
        description="Username for the account"
    )

    email: EmailStr = Field(
        ...,                   # Required - tại sao? → Cần để login và contact
        description="Valid email address"
        # EmailStr tự động validate format
    )

    password: str = Field(
        ...,                   # Required - tại sao? → Cần để authentication
        min_length=8,          # Tại sao 8? → NIST security recommendation
        description="Password with at least 8 characters"
        # Validation phức tạp hơn sẽ ở validator function
    )

# 🤔 Tại sao không validate password complexity trong schema?
# → Schema chỉ validate format cơ bản
# → Business logic validation (complexity) ở Service layer
# → Tách biệt concerns: Schema = format, Service = business rules
```

#### **Bước 4: Thiết kế Response Schema Strategy**

**Cách suy luận response design:**
```python
# 🤔 Response nào user-friendly và developer-friendly?

# ❌ Inconsistent responses:
# API 1: {"user": {...}, "status": "ok"}
# API 2: {"data": {...}, "success": true}
# API 3: {"result": {...}, "message": "done"}

# ✅ Consistent response pattern:
class SuccessResponse(BaseModel, Generic[T]):
    """
    Suy luận design decisions:

    1. success: bool → Frontend dễ check if (response.success)
    2. message: str → User-friendly message để hiển thị
    3. data: T → Actual payload, generic type cho flexibility
    4. timestamp: datetime → Debugging, audit trail
    """
    success: bool = Field(default=True)
    message: str = Field(..., description="User-friendly message")
    data: Optional[T] = Field(None, description="Response payload")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

# 🤔 Tại sao cần Generic[T]?
# → Một response format cho nhiều data types:
# SuccessResponse[UserResponse]
# SuccessResponse[List[UserResponse]]
# SuccessResponse[dict]
```

#### **Bước 5: Schema Inheritance Strategy**

**Cách suy luận inheritance:**
```python
# 🤔 Schemas nào có chung attributes?

# Base schema chứa common fields
class UserBase(BaseModel):
    """
    Suy luận: Fields nào xuất hiện ở nhiều schemas?
    - email: Có ở UserCreate, UserUpdate, UserResponse
    - username: Có ở UserCreate, UserUpdate, UserResponse

    → Tạo UserBase để tránh duplicate
    """
    email: EmailStr
    username: Optional[str] = None

# Inherit và extend
class UserCreate(UserBase):
    """Extend base với fields chỉ cần khi tạo mới"""
    username: str = Field(..., min_length=2, max_length=50)  # Override để required
    password: str = Field(..., min_length=8)                # Chỉ cần khi create

class UserUpdate(UserBase):
    """Extend base với fields có thể update"""
    # Tất cả fields optional cho partial update
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    password: Optional[str] = None

class UserResponse(UserBase):
    """Extend base với fields trả về client"""
    user_id: uuid.UUID      # Thêm ID để client reference
    created_at: datetime    # Thêm metadata
    # Không có password → Security
```

#### **Bước 6: Validation Error Handling Design**

**Cách suy luận error response:**
```python
# 🤔 Khi validation fail, frontend cần gì?

class ValidationErrorResponse(BaseModel):
    """
    Suy luận requirements:
    1. Frontend cần biết field nào lỗi → field_errors
    2. Frontend cần message để hiển thị → user-friendly messages
    3. Developer cần debug → error codes, details
    """
    success: bool = Field(default=False)
    message: str = Field(default="Validation failed")
    error_code: str = Field(default="VALIDATION_ERROR")
    field_errors: Dict[str, List[str]] = Field(default_factory=dict)

    # Example:
    # {
    #     "success": false,
    #     "message": "Please fix the following errors",
    #     "error_code": "VALIDATION_ERROR",
    #     "field_errors": {
    #         "email": ["Invalid email format"],
    #         "password": ["Password too short", "Password must contain numbers"]
    #     }
    # }

# 🤔 Tại sao field_errors là Dict[str, List[str]]?
# → Một field có thể có nhiều lỗi validation
# → Frontend có thể hiển thị tất cả lỗi của một field
```

### **🔄 Phân loại Schemas:**

```
📥 INPUT SCHEMAS (Client → Server):
├── UserCreate     - Đăng ký user mới
├── UserLogin      - Đăng nhập  
├── UserUpdate     - Cập nhật thông tin user
└── UserProfileUpdate - Cập nhật profile

📤 OUTPUT SCHEMAS (Server → Client):
├── UserResponse   - Thông tin user trả về
├── UserRead       - Thông tin đầy đủ (admin)
└── UserProfileResponse - Thông tin profile

🔄 INTERNAL SCHEMAS:
├── TokenPayload   - Dữ liệu trong JWT
└── TokenData      - Token đã decode
```

### **📝 Ví dụ chi tiết: User Schemas**

```python
# backend/app/modules/auth/schemas/user.py

from pydantic import BaseModel, EmailStr, Field
from typing import Optional
import uuid
from datetime import datetime

class UserBase(BaseModel):
    """
    Base schema - Chứa fields chung
    
    Tại sao cần Base?
    - Tránh lặp code
    - Dễ maintain khi thay đổi
    - Inheritance pattern
    """
    email: EmailStr  # Tự động validate email format
    username: Optional[str] = None

class UserCreate(UserBase):
    """
    Schema cho đăng ký user mới
    
    Input từ client:
    {
        "username": "john_doe",
        "email": "<EMAIL>", 
        "password": "Password123!"
    }
    """
    username: str = Field(
        ...,                    # Required (bắt buộc)
        min_length=2,          # Tối thiểu 2 ký tự
        max_length=50,         # Tối đa 50 ký tự
        description="Username for the account"
    )
    email: EmailStr            # Override từ UserBase
    password: str = Field(
        ..., 
        min_length=8, 
        description="Password with at least 8 characters"
    )

class UserLogin(BaseModel):
    """
    Schema cho đăng nhập
    
    Input từ client:
    {
        "email": "<EMAIL>",
        "password": "Password123!"
    }
    """
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    """
    Schema trả về client sau khi đăng ký/đăng nhập
    
    Output về client:
    {
        "user_id": "123e4567-e89b-12d3-a456-************",
        "username": "john_doe",
        "email": "<EMAIL>",
        "created_at": "2025-09-14T10:30:00Z",
        "full_name": "John Doe Smith",
        "phone": "+***********",
        "avatar_url": "https://example.com/avatar.jpg"
    }
    """
    user_id: uuid.UUID
    email: EmailStr
    username: Optional[str]
    created_at: datetime
    
    # Profile information (từ UserProfile)
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    
    class Config:
        from_attributes = True  # Cho phép convert từ SQLModel
```

### **🔍 Hiểu về Pydantic Validation**

```python
# Ví dụ validation tự động:

# ✅ Valid data
user_data = UserCreate(
    username="john_doe",
    email="<EMAIL>",
    password="Password123!"
)

# ❌ Invalid data - Pydantic sẽ raise ValidationError
try:
    user_data = UserCreate(
        username="j",                    # Quá ngắn (min_length=2)
        email="invalid-email",           # Không phải email
        password="123"                   # Quá ngắn (min_length=8)
    )
except ValidationError as e:
    print(e.errors())
    # [
    #     {'loc': ('username',), 'msg': 'ensure this value has at least 2 characters'},
    #     {'loc': ('email',), 'msg': 'value is not a valid email address'},
    #     {'loc': ('password',), 'msg': 'ensure this value has at least 8 characters'}
    # ]
```

### **🎯 Response Schemas Pattern**

```python
# backend/app/shared/schemas/response.py

class SuccessResponse(BaseModel, Generic[T]):
    """
    Standardized success response
    
    Format cố định cho mọi API success:
    {
        "success": true,
        "message": "Thao tác thành công",
        "data": { ... },
        "timestamp": "2025-09-14T10:30:00Z"
    }
    """
    success: bool = Field(default=True)
    message: str = Field(..., description="Thông báo thành công")
    data: Optional[T] = Field(None, description="Dữ liệu trả về")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class ErrorResponse(BaseModel):
    """
    Standardized error response
    
    Format cố định cho mọi API error:
    {
        "success": false,
        "message": "Có lỗi xảy ra",
        "error_code": "AUTH_002",
        "error_details": { ... },
        "timestamp": "2025-09-14T10:30:00Z"
    }
    """
    success: bool = Field(default=False)
    message: str = Field(..., description="Thông báo lỗi")
    error_code: Optional[str] = Field(None, description="Mã lỗi để debug")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Chi tiết lỗi")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
```

### **🤔 Tại sao cần Response chuẩn?**

**Trước khi có chuẩn:**
```python
# API 1 trả về:
{"user": {...}, "message": "OK"}

# API 2 trả về:  
{"data": {...}, "status": "success"}

# API 3 trả về:
{"result": {...}, "msg": "Done"}
```

**Sau khi có chuẩn:**
```python
# Tất cả APIs đều trả về:
{
    "success": true/false,
    "message": "...",
    "data": {...},
    "timestamp": "..."
}
```

**Lợi ích:**
- ✅ Frontend chỉ cần 1 cách parse response
- ✅ Dễ debug và log
- ✅ Professional, nhất quán
- ✅ Dễ maintain và extend

---

## 4. **EXCEPTIONS - XỬ LÝ LỖI CHUYÊN NGHIỆP**

### **🎯 Exception là gì?**

Exception là cách **"báo lỗi có kiểm soát"**. Thay vì app crash, ta throw exception để:
- Thông báo lỗi rõ ràng cho user
- Log lỗi để developer debug
- Trả về HTTP status code phù hợp

### **🧠 CÁCH SUY LUẬN VÀ THIẾT KẾ EXCEPTIONS**

#### **Bước 1: Phân tích User Stories và xác định lỗi có thể xảy ra**

**Ví dụ User Story**: "Là user, tôi muốn đăng ký tài khoản với email và password"

**Suy luận các trường hợp lỗi:**
```
🤔 Câu hỏi: Điều gì có thể sai khi user đăng ký?

1. Email đã được người khác sử dụng → EmailAlreadyExistsError
2. Password quá yếu (< 8 ký tự, không có số) → WeakPasswordError
3. Email không đúng format → InvalidEmailFormatError
4. Username đã tồn tại → UsernameAlreadyExistsError
5. Username có ký tự đặc biệt không hợp lệ → InvalidUsernameFormatError
6. Database không kết nối được → DatabaseConnectionError
7. Server hết memory → InternalServerError
```

**Nguyên tắc suy luận:**
- **Input validation errors**: Dữ liệu user nhập không hợp lệ
- **Business rule violations**: Vi phạm quy tắc nghiệp vụ (email trùng)
- **System errors**: Lỗi hệ thống (DB, network, memory)

#### **Bước 2: Phân loại và đặt tên Exception**

**Quy tắc đặt tên Exception:**
```
[Domain][Problem][Error]

Ví dụ:
- EmailAlreadyExistsError = Email + Already Exists + Error
- WeakPasswordError = Weak + Password + Error
- InvalidCredentialsError = Invalid + Credentials + Error
- DatabaseConnectionError = Database + Connection + Error
```

**Phân loại theo domain:**
```
📁 auth_exceptions.py:
├── EmailAlreadyExistsError      # Email đã tồn tại
├── WeakPasswordError            # Password yếu
├── InvalidCredentialsError      # Sai email/password
├── AccountDeactivatedError      # Tài khoản bị khóa
└── VerificationRequiredError    # Chưa verify email

📁 user_exceptions.py:
├── UserNotFoundError           # Không tìm thấy user
├── InvalidUserDataError        # Dữ liệu user không hợp lệ
├── ProfileNotFoundError        # Không tìm thấy profile
└── InvalidPhoneFormatError     # Số điện thoại sai format

📁 system_exceptions.py:
├── DatabaseConnectionError     # Lỗi kết nối DB
├── InternalServerError        # Lỗi server
├── ExternalServiceError       # Lỗi service bên ngoài
└── RateLimitExceededError     # Vượt quá rate limit
```

#### **Bước 3: Thiết kế Error Code System**

**Cách suy luận Error Codes:**
```
🤔 Tại sao cần error codes?
- Frontend cần biết loại lỗi để hiển thị UI phù hợp
- Developer cần track và debug lỗi
- Support team cần hiểu lỗi để hỗ trợ user
- Analytics cần thống kê lỗi

Format: [DOMAIN]_[NUMBER]
- AUTH_001, AUTH_002, AUTH_003...
- USER_001, USER_002, USER_003...
- SYS_001, SYS_002, SYS_003...
```

**Mapping Logic:**
```python
# Suy luận: Lỗi authentication quan trọng nhất là gì?
AUTH_001 = "INVALID_CREDENTIALS"     # Sai email/password (phổ biến nhất)
AUTH_002 = "EMAIL_ALREADY_EXISTS"    # Email trùng (khi đăng ký)
AUTH_003 = "USER_NOT_FOUND"          # Không tìm thấy user
AUTH_004 = "WEAK_PASSWORD"           # Password yếu
AUTH_005 = "ACCOUNT_DEACTIVATED"     # Tài khoản bị khóa

# Tại sao AUTH_001 là INVALID_CREDENTIALS?
# → Đây là lỗi phổ biến nhất khi user đăng nhập sai
```

#### **Bước 4: Thiết kế Message System**

**Cách suy luận Messages:**
```
🤔 Message nào user-friendly nhất?

❌ Technical message: "Foreign key constraint violation"
✅ User-friendly: "Email already registered"

❌ Vague message: "Error occurred"
✅ Specific message: "Password must be at least 8 characters"

❌ English only: "Invalid email format"
✅ Multilingual ready: Dùng constants để dễ translate
```

**Message Design Pattern:**
```python
class Messages:
    # Nguyên tắc: Rõ ràng, hướng dẫn user làm gì tiếp theo

    # ❌ Không tốt
    EMAIL_ERROR = "Email error"

    # ✅ Tốt - Rõ ràng + Hướng dẫn
    EMAIL_ALREADY_EXISTS = "Email already registered. Please use a different email or login."
    WEAK_PASSWORD = "Password must be at least 8 characters with numbers and letters."
    INVALID_EMAIL_FORMAT = "Please enter a valid email address."
```

#### **Bước 5: Thiết kế Exception Hierarchy**

**Cách suy luận Inheritance:**
```python
# 🤔 Câu hỏi: Exception nào có chung đặc điểm?

# Tất cả auth exceptions đều có:
# - error_code
# - detail message
# - HTTP status code (thường là 400)

class BaseAuthException(Exception):
    """Base cho tất cả auth exceptions"""
    def __init__(self, detail: str, error_code: str, status_code: int = 400):
        self.detail = detail
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(detail)

# Specific exceptions inherit từ base
class EmailAlreadyExistsError(BaseAuthException):
    def __init__(self):
        super().__init__(
            detail=Messages.EMAIL_ALREADY_EXISTS,
            error_code=ErrorCodes.EMAIL_EXISTS,
            status_code=409  # Conflict
        )
```

#### **Bước 6: Validation Logic Design**

**Cách suy luận validation rules:**
```python
# 🤔 Email hợp lệ khi nào?
def validate_email(email: str) -> dict:
    """
    Suy luận validation rules:
    1. Có @ symbol
    2. Có domain (.com, .org, etc.)
    3. Không có ký tự đặc biệt không hợp lệ
    4. Độ dài hợp lý (< 254 characters theo RFC)
    """

    if not email or "@" not in email:
        return {"is_valid": False, "message": "Email must contain @ symbol"}

    if len(email) > 254:
        return {"is_valid": False, "message": "Email too long"}

    # Regex pattern suy luận từ RFC 5322
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        return {"is_valid": False, "message": "Invalid email format"}

    return {"is_valid": True, "message": "Valid email"}

# 🤔 Password mạnh khi nào?
def validate_password_strength(password: str) -> dict:
    """
    Suy luận từ security best practices:
    1. Ít nhất 8 ký tự (NIST recommendation)
    2. Có chữ hoa và chữ thường (complexity)
    3. Có số (complexity)
    4. Có ký tự đặc biệt (optional nhưng tốt)
    5. Không phải common passwords
    """

    if len(password) < 8:
        return {"is_valid": False, "message": "Password must be at least 8 characters"}

    if not re.search(r'[A-Z]', password):
        return {"is_valid": False, "message": "Password must contain uppercase letter"}

    if not re.search(r'[a-z]', password):
        return {"is_valid": False, "message": "Password must contain lowercase letter"}

    if not re.search(r'\d', password):
        return {"is_valid": False, "message": "Password must contain number"}

    # Common passwords check
    common_passwords = ['password', '123456', 'qwerty', 'admin']
    if password.lower() in common_passwords:
        return {"is_valid": False, "message": "Password too common"}

    return {"is_valid": True, "message": "Strong password"}
```

### **🎯 Practical Example: Thiết kế Exception cho Register Flow**

**Step-by-step thinking process:**

```python
# 🤔 User story: "User đăng ký tài khoản"
# Phân tích: Điều gì có thể sai?

def analyze_register_flow():
    """
    Suy luận từ user journey:

    1. User nhập username → Username có hợp lệ không?
    2. User nhập email → Email có đúng format không? Đã tồn tại chưa?
    3. User nhập password → Password có đủ mạnh không?
    4. System tạo user → Database có lỗi không?
    5. System tạo profile → Profile tạo được không?
    """

    potential_errors = [
        # Input validation (client-side có thể miss)
        "InvalidUsernameFormatError",    # Username có ký tự đặc biệt
        "InvalidEmailFormatError",       # Email sai format
        "WeakPasswordError",             # Password yếu

        # Business rules (server-side logic)
        "EmailAlreadyExistsError",       # Email đã được dùng
        "UsernameAlreadyExistsError",    # Username đã được dùng

        # System errors (infrastructure)
        "DatabaseConnectionError",       # DB không kết nối được
        "ProfileCreationError",          # Tạo profile thất bại

        # External dependencies
        "EmailServiceError",             # Gửi email verify thất bại
    ]

    return potential_errors

# 🤔 Làm sao biết exception nào quan trọng nhất?
# → Dựa vào tần suất xảy ra và impact đến user

priority_mapping = {
    "HIGH": ["EmailAlreadyExistsError", "WeakPasswordError"],      # Xảy ra thường xuyên
    "MEDIUM": ["InvalidEmailFormatError", "UsernameAlreadyExistsError"],  # Ít xảy ra hơn
    "LOW": ["DatabaseConnectionError", "EmailServiceError"]        # Hiếm xảy ra
}
```

### **🏗️ Kiến trúc Exception System**

```
📁 utils/exceptions/
├── auth_exceptions.py      # Lỗi liên quan authentication
├── user_exceptions.py      # Lỗi liên quan user data
└── base_exceptions.py      # Base exception classes

📁 utils/constants/
├── error_codes.py          # Mã lỗi chuẩn
└── messages.py            # Thông báo lỗi chuẩn
```

### **📝 Ví dụ: Auth Exceptions**

```python
# backend/app/utils/exceptions/auth_exceptions.py

from app.utils.constants.error_codes import ErrorCodes
from app.utils.constants.messages import Messages

class BaseAuthException(Exception):
    """Base exception cho tất cả auth errors"""
    def __init__(self, detail: str, error_code: str):
        self.detail = detail
        self.error_code = error_code
        super().__init__(detail)

class EmailAlreadyExistsError(BaseAuthException):
    """Lỗi email đã tồn tại khi đăng ký"""
    def __init__(self):
        super().__init__(
            detail=Messages.EMAIL_ALREADY_EXISTS,  # "Email already registered"
            error_code=ErrorCodes.EMAIL_EXISTS      # "AUTH_002"
        )

class WeakPasswordError(BaseAuthException):
    """Lỗi password không đủ mạnh"""
    def __init__(self):
        super().__init__(
            detail=Messages.WEAK_PASSWORD,          # "Password does not meet requirements"
            error_code=ErrorCodes.WEAK_PASSWORD     # "AUTH_004"
        )

class InvalidCredentialsError(BaseAuthException):
    """Lỗi đăng nhập sai email/password"""
    def __init__(self):
        super().__init__(
            detail=Messages.INVALID_CREDENTIALS,    # "Invalid email or password"
            error_code=ErrorCodes.INVALID_CREDS     # "AUTH_001"
        )
```

### **📋 Constants - Tập trung hóa thông báo**

```python
# backend/app/utils/constants/messages.py

class Messages:
    """Tập trung tất cả thông báo trong app"""

    # Success messages
    REGISTER_SUCCESS = "Đăng ký thành công"
    LOGIN_SUCCESS = "Đăng nhập thành công"
    PROFILE_UPDATED = "Cập nhật profile thành công"

    # Error messages
    EMAIL_ALREADY_EXISTS = "Email already registered"
    INVALID_CREDENTIALS = "Invalid email or password"
    WEAK_PASSWORD = "Password does not meet requirements"
    USER_NOT_FOUND = "User not found"
    ACCOUNT_DEACTIVATED = "Account has been deactivated"

# backend/app/utils/constants/error_codes.py

class ErrorCodes:
    """Mã lỗi chuẩn để debug và tracking"""

    # Authentication errors (AUTH_xxx)
    INVALID_CREDS = "AUTH_001"
    EMAIL_EXISTS = "AUTH_002"
    USER_NOT_FOUND = "AUTH_003"
    WEAK_PASSWORD = "AUTH_004"
    ACCOUNT_DEACTIVATED = "AUTH_005"

    # User data errors (USER_xxx)
    INVALID_DATA = "USER_001"
    VALIDATION_ERROR = "USER_002"

    # System errors (SYS_xxx)
    DATABASE_ERROR = "SYS_001"
    INTERNAL_ERROR = "SYS_002"
```

### **🎯 Cách sử dụng Exceptions**

```python
# Trong Service layer:
async def create_user(self, user_data: UserCreate) -> User:
    # Check email đã tồn tại chưa
    existing_user = await self.get_user_by_email(user_data.email)
    if existing_user:
        raise EmailAlreadyExistsError()  # Throw exception

    # Validate password strength
    if not validate_password_strength(user_data.password):
        raise WeakPasswordError()

    # Tạo user...
    return user

# Trong Controller layer:
async def register(self, user_data: UserCreate):
    try:
        user = await self.auth_service.create_user(user_data)
        return SuccessResponse(
            message=Messages.REGISTER_SUCCESS,
            data=UserResponse.from_orm(user)
        )

    except EmailAlreadyExistsError as e:
        return ErrorResponse(
            message=e.detail,           # "Email already registered"
            error_code=e.error_code,    # "AUTH_002"
            error_details={"email": user_data.email}
        )

    except WeakPasswordError as e:
        return ErrorResponse(
            message=e.detail,
            error_code=e.error_code
        )
```

### **🔍 Lợi ích của Exception Pattern**

**❌ Trước khi có Exception system:**
```python
# Code rối rắm, khó maintain
async def create_user(self, user_data):
    if existing_email:
        return {"error": "Email đã tồn tại", "code": 400}
    if weak_password:
        return {"error": "Password yếu", "code": 400}
    # ... nhiều if-else
```

**✅ Sau khi có Exception system:**
```python
# Code sạch, rõ ràng
async def create_user(self, user_data):
    if existing_email:
        raise EmailAlreadyExistsError()
    if weak_password:
        raise WeakPasswordError()
    # Logic chính không bị làm rối
```

---

## 5. **SERVICES - LOGIC NGHIỆP VỤ**

### **🎯 Service Layer là gì?**

Service layer là **"bộ não"** của ứng dụng, chứa tất cả business logic:
- Xử lý dữ liệu phức tạp
- Tương tác với database
- Validation nghiệp vụ
- Gọi external APIs

### **🧠 CÁCH SUY LUẬN VÀ THIẾT KẾ SERVICES**

#### **Bước 1: Phân tích Business Operations**

**Ví dụ Domain**: Authentication Service

**Suy luận operations:**
```python
# 🤔 Câu hỏi: User có thể làm gì với authentication?

"""
User Stories Analysis:
1. "Tôi muốn đăng ký tài khoản" → create_user()
2. "Tôi muốn đăng nhập" → authenticate_user()
3. "Tôi muốn đổi password" → change_password()
4. "Tôi quên password" → reset_password()
5. "Tôi muốn xóa tài khoản" → deactivate_user()
"""

class AuthService:
    """
    Suy luận service responsibilities:
    - User lifecycle management (CRUD)
    - Authentication logic
    - Password management
    - Account status management
    """

    def __init__(self, db: AsyncSession):
        self.db = db  # 🤔 Tại sao inject DB? → Dependency inversion, testable
```

#### **Bước 2: Thiết kế Method Signatures**

**Cách suy luận method design:**
```python
# 🤔 create_user() method nên như thế nào?

async def create_user(self, user_data: UserCreate) -> User:
    """
    Suy luận signature:

    Input: UserCreate schema
    - 🤔 Tại sao UserCreate? → Chỉ cần data để tạo user
    - 🤔 Tại sao không dict? → Type safety, validation

    Output: User model
    - 🤔 Tại sao User model? → Controller cần full user data
    - 🤔 Tại sao không UserResponse? → Service trả raw data, Controller format response

    Async:
    - 🤔 Tại sao async? → Database operations are I/O bound
    """
    pass

async def authenticate_user(self, email: str, password: str) -> User:
    """
    Suy luận signature:

    Input: email, password (primitives)
    - 🤔 Tại sao không UserLogin schema? → Simple operation, không cần schema overhead
    - 🤔 Tại sao tách riêng? → Clear intent, dễ hiểu

    Output: User model
    - 🤔 Tại sao không bool? → Controller cần user data để tạo response
    - 🤔 Tại sao không Optional[User]? → Throw exception nếu fail, cleaner error handling
    """
    pass
```

#### **Bước 3: Thiết kế Business Logic Flow**

**Cách suy luận business flow:**
```python
async def create_user(self, user_data: UserCreate) -> User:
    """
    Suy luận business flow từ requirements:

    🤔 Điều gì cần xảy ra khi tạo user?
    1. Validate input data
    2. Check business rules (uniqueness)
    3. Transform data (hash password)
    4. Persist to database
    5. Create related entities (profile)
    6. Return result
    """

    # Step 1: Input validation
    # 🤔 Tại sao validate ở service? → Business rules, không chỉ format
    email_validation = validate_email(user_data.email)
    if not email_validation["is_valid"]:
        raise InvalidDataError(email_validation["message"])

    # Step 2: Business rules
    # 🤔 Tại sao check uniqueness? → Business rule: một email một tài khoản
    existing_user = await self.get_user_by_email(user_data.email)
    if existing_user:
        raise EmailAlreadyExistsError()

    # Step 3: Data transformation
    # 🤔 Tại sao hash password? → Security: không lưu plain text
    hashed_password = hash_password(user_data.password)

    # Step 4: Database operations
    # 🤔 Tại sao dùng transaction? → Đảm bảo data consistency
    user = User(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password
    )

    self.db.add(user)
    await self.db.commit()
    await self.db.refresh(user)  # 🤔 Tại sao refresh? → Lấy generated fields (id, timestamps)

    # Step 5: Create related entities
    # 🤔 Tại sao tạo profile ngay? → User luôn cần có profile, dù empty
    profile = UserProfile(user_id=user.id)
    self.db.add(profile)
    await self.db.commit()

    return user
```

#### **Bước 4: Thiết kế Error Handling Strategy**

**Cách suy luận error handling:**
```python
async def authenticate_user(self, email: str, password: str) -> User:
    """
    Suy luận error scenarios:

    🤔 Điều gì có thể sai khi authenticate?
    1. User không tồn tại → InvalidCredentialsError (không nói cụ thể)
    2. Password sai → InvalidCredentialsError (không nói cụ thể)
    3. Account bị khóa → AccountDeactivatedError
    4. Database lỗi → DatabaseError (let it bubble up)
    """

    try:
        # Find user
        user = await self.get_user_by_email(email)
        if not user:
            # 🤔 Tại sao không nói "user not found"? → Security: không leak info
            raise InvalidCredentialsError()

        # Check account status
        if not user.is_active:
            # 🤔 Tại sao specific error? → User cần biết tài khoản bị khóa
            raise AccountDeactivatedError()

        # Verify password
        if not verify_password(password, user.hashed_password):
            # 🤔 Tại sao cùng error với user not found? → Security: không leak info
            raise InvalidCredentialsError()

        return user

    except (InvalidCredentialsError, AccountDeactivatedError):
        # 🤔 Tại sao re-raise? → Let controller handle specific errors
        raise
    except Exception as e:
        # 🤔 Tại sao log và re-raise? → Debug info + let upper layer handle
        logger.error(f"Unexpected error in authenticate_user: {str(e)}")
        raise
```

#### **Bước 5: Thiết kế Database Query Strategy**

**Cách suy luận query design:**
```python
async def get_user_by_id_with_profile(self, user_id: uuid.UUID) -> Optional[User]:
    """
    Suy luận query requirements:

    🤔 Controller cần gì để tạo UserResponse?
    - User basic info (username, email, created_at)
    - Profile info (full_name, phone, avatar_url)

    🤔 Làm sao lấy data hiệu quả?
    - Option 1: 2 queries (user + profile) → 2 DB calls
    - Option 2: JOIN query → 1 DB call, better performance
    """

    sql = text("""
        SELECT
            -- User fields
            u.id, u.username, u.email, u.hashed_password,
            u.is_active, u.is_verified, u.created_at, u.updated_at,

            -- Profile fields (with aliases to avoid conflicts)
            p.id as profile_id,
            p.full_name, p.phone, p.date_of_birth,
            p.gender, p.address, p.avatar_url,
            p.created_at as profile_created_at,
            p.updated_at as profile_updated_at

        FROM users u
        LEFT JOIN user_profiles p ON u.id = p.user_id  -- 🤔 LEFT JOIN? → User có thể chưa có profile
        WHERE u.id = :user_id
    """)

    result = await self.db.execute(sql, {"user_id": user_id})
    row = result.fetchone()

    if not row:
        return None

    # 🤔 Tại sao manual mapping? → SQLModel relationships phức tạp với raw SQL
    user = User.model_validate(dict(row))

    # Create profile if exists
    if row["profile_id"]:
        profile_data = {
            "id": row["profile_id"],
            "user_id": row["id"],
            "full_name": row["full_name"],
            # ... other profile fields
        }
        user.profile = UserProfile.model_validate(profile_data)

    return user
```

### **🏗️ Cấu trúc AuthService**

```python
# backend/app/modules/auth/services/auth.py

class AuthService:
    """
    Service xử lý tất cả logic liên quan authentication

    Nhiệm vụ:
    - Tạo user mới (đăng ký)
    - Xác thực user (đăng nhập)
    - Quản lý user data
    - Hash/verify password
    - Tương tác với database
    """

    def __init__(self, db: AsyncSession):
        self.db = db  # Database session

    async def create_user(self, user_data: UserCreate) -> User:
        """
        Tạo user mới - Logic đăng ký

        Steps:
        1. Validate input data
        2. Check email/username uniqueness
        3. Hash password
        4. Create user record
        5. Create profile record
        6. Return user object
        """

        # Step 1: Validate input
        email_validation = validate_email(user_data.email)
        if not email_validation["is_valid"]:
            raise InvalidDataError(email_validation["message"])

        username_validation = validate_username(user_data.username)
        if not username_validation["is_valid"]:
            raise InvalidDataError(username_validation["message"])

        password_validation = validate_password_strength(user_data.password)
        if not password_validation["is_valid"]:
            raise WeakPasswordError()

        # Step 2: Check uniqueness
        existing_email = await self.get_user_by_email(user_data.email)
        if existing_email:
            raise EmailAlreadyExistsError()

        existing_username = await self.get_user_by_username(user_data.username)
        if existing_username:
            raise UsernameAlreadyExistsError()

        # Step 3: Hash password
        hashed_password = hash_password(user_data.password)

        # Step 4: Create user
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            is_active=True,
            is_verified=False
        )

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)

        # Step 5: Create empty profile
        profile = UserProfile(user_id=user.id)
        self.db.add(profile)
        await self.db.commit()

        return user

    async def authenticate_user(self, email: str, password: str) -> User:
        """
        Xác thực user - Logic đăng nhập

        Steps:
        1. Find user by email
        2. Check if user exists & active
        3. Verify password
        4. Return user if valid
        """

        # Step 1: Find user
        user = await self.get_user_by_email(email)
        if not user:
            raise InvalidCredentialsError()

        # Step 2: Check active status
        if not user.is_active:
            raise AccountDeactivatedError()

        # Step 3: Verify password
        if not verify_password(password, user.hashed_password):
            raise InvalidCredentialsError()

        return user

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Tìm user theo email"""
        sql = text("SELECT * FROM users WHERE email = :email")
        result = await self.db.execute(sql, {"email": email})
        row = result.fetchone()

        if row:
            return User.model_validate(dict(row))
        return None

    async def get_user_by_id_with_profile(self, user_id: uuid.UUID) -> Optional[User]:
        """
        Lấy user kèm profile information

        Sử dụng JOIN để lấy data từ 2 tables cùng lúc
        """
        sql = text("""
            SELECT
                u.*,
                p.id as profile_id,
                p.full_name,
                p.phone,
                p.date_of_birth,
                p.gender,
                p.address,
                p.avatar_url,
                p.created_at as profile_created_at,
                p.updated_at as profile_updated_at
            FROM users u
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE u.id = :user_id
        """)

        result = await self.db.execute(sql, {"user_id": user_id})
        row = result.fetchone()

        if not row:
            return None

        # Tạo User object
        user_data = {
            "id": row["id"],
            "username": row["username"],
            "email": row["email"],
            "hashed_password": row["hashed_password"],
            "is_active": row["is_active"],
            "is_verified": row["is_verified"],
            "created_at": row["created_at"],
            "updated_at": row["updated_at"]
        }
        user = User.model_validate(user_data)

        # Tạo Profile object nếu có
        if row["profile_id"]:
            profile_data = {
                "id": row["profile_id"],
                "user_id": row["id"],
                "full_name": row["full_name"],
                "phone": row["phone"],
                "date_of_birth": row["date_of_birth"],
                "gender": row["gender"],
                "address": row["address"],
                "avatar_url": row["avatar_url"],
                "created_at": row["profile_created_at"],
                "updated_at": row["profile_updated_at"]
            }
            user.profile = UserProfile.model_validate(profile_data)

        return user
```

### **🔍 Tại sao cần Service Layer?**

**❌ Không có Service (Controller trực tiếp gọi DB):**
```python
# Controller phình to, khó test, khó maintain
async def register(self, user_data):
    # 50 lines validation logic
    # 30 lines database operations
    # 20 lines business rules
    # Controller trở thành "God Class"
```

**✅ Có Service Layer:**
```python
# Controller gọn gàng, dễ hiểu
async def register(self, user_data):
    try:
        user = await self.auth_service.create_user(user_data)
        return SuccessResponse(data=user)
    except Exception as e:
        return ErrorResponse(message=str(e))

# Service chứa logic phức tạp
class AuthService:
    async def create_user(self, user_data):
        # All business logic here
```

### **🎯 Service Best Practices**

1. **Single Responsibility**: Mỗi service chỉ lo 1 domain
2. **Dependency Injection**: Inject database session
3. **Error Handling**: Throw exceptions, không return error codes
4. **Async/Await**: Sử dụng async cho database operations
5. **Logging**: Log các operations quan trọng
6. **Transaction**: Sử dụng database transactions cho operations phức tạp

---

## 6. **CONTROLLERS - ĐIỀU KHIỂN LUỒNG XỬ LÝ**

### **🎯 Controller là gì?**

Controller là **"người điều phối"** giữa API routes và business logic:
- Nhận request từ routes
- Gọi services để xử lý
- Handle exceptions
- Tạo response chuẩn
- Validate input data

### **🧠 CÁCH SUY LUẬN VÀ THIẾT KẾ CONTROLLERS**

#### **Bước 1: Phân tích Controller Responsibilities**

**Suy luận Controller pattern:**
```python
# 🤔 Controller nên làm gì và KHÔNG nên làm gì?

"""
✅ Controller SHOULD do:
1. Receive và validate request data
2. Call appropriate service methods
3. Handle service exceptions
4. Transform service results to API responses
5. Set appropriate HTTP status codes

❌ Controller SHOULD NOT do:
1. Business logic (belongs to Service)
2. Database operations (belongs to Service)
3. Complex data transformations (belongs to Service)
4. External API calls (belongs to Service)
"""

class AuthController:
    """
    Suy luận: AuthController chỉ lo việc điều phối
    - Thin controller, fat service
    - Single responsibility: HTTP request/response handling
    """

    def __init__(self, db: AsyncSession):
        self.auth_service = AuthService(db)  # 🤔 Tại sao inject service? → Separation of concerns
```

#### **Bước 2: Thiết kế Method Flow Pattern**

**Cách suy luận controller method structure:**
```python
async def register(self, user_data: UserCreate) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
    """
    Suy luận standard controller flow:

    🤔 Controller method nên có structure như thế nào?
    1. Try block: Happy path
    2. Specific exception handling: Known errors
    3. Generic exception handling: Unexpected errors
    4. Always return consistent response format
    """

    try:
        # Step 1: Call service (business logic)
        # 🤔 Tại sao không validate ở đây? → Service đã validate business rules
        user = await self.auth_service.create_user(user_data)

        # Step 2: Get additional data if needed
        # 🤔 Tại sao cần get thêm data? → Response cần profile info
        user_with_profile = await self.auth_service.get_user_by_id_with_profile(user.id)

        # Step 3: Transform to response format
        # 🤔 Tại sao transform ở controller? → Controller lo response format
        user_response = UserResponse(
            user_id=user_with_profile.id,
            email=user_with_profile.email,
            username=user_with_profile.username,
            created_at=user_with_profile.created_at,
            # Profile data (có thể None)
            full_name=user_with_profile.profile.full_name if user_with_profile.profile else None,
            phone=user_with_profile.profile.phone if user_with_profile.profile else None,
            avatar_url=user_with_profile.profile.avatar_url if user_with_profile.profile else None
        )

        # Step 4: Return success response
        return SuccessResponse(
            message=Messages.REGISTER_SUCCESS,
            data=user_response
        )

    # Step 5: Handle specific exceptions
    except EmailAlreadyExistsError as e:
        # 🤔 Tại sao specific handling? → Different errors need different responses
        return ErrorResponse(
            message=e.detail,
            error_code=e.error_code,
            error_details={"email": user_data.email}  # 🤔 Tại sao thêm details? → Help frontend highlight field
        )

    except WeakPasswordError as e:
        return ErrorResponse(
            message=e.detail,
            error_code=e.error_code
        )

    # Step 6: Handle unexpected exceptions
    except Exception as e:
        # 🤔 Tại sao log? → Debug unexpected errors
        logger.error(f"Unexpected error in register: {str(e)}")
        return ErrorResponse(
            message="Có lỗi xảy ra, vui lòng thử lại",
            error_code="INTERNAL_ERROR"
        )
```

#### **Bước 3: Thiết kế Response Transformation Strategy**

**Cách suy luận response mapping:**
```python
# 🤔 Làm sao map từ Service result sang API response?

def _create_user_response(self, user: User) -> UserResponse:
    """
    Suy luận transformation logic:

    🤔 UserResponse cần gì?
    - Basic user info: id, username, email, created_at
    - Profile info: full_name, phone, avatar_url
    - Security: Không trả về password, is_active, is_verified
    """

    return UserResponse(
        user_id=user.id,
        email=user.email,
        username=user.username,
        created_at=user.created_at,

        # Safe profile access
        # 🤔 Tại sao check user.profile? → Profile có thể None
        full_name=user.profile.full_name if user.profile else None,
        phone=user.profile.phone if user.profile else None,
        avatar_url=user.profile.avatar_url if user.profile else None
    )

# 🤔 Tại sao tách thành method riêng?
# → DRY principle: Dùng ở nhiều controller methods
# → Easier testing: Test transformation logic riêng
# → Single responsibility: Mỗi method một việc
```

#### **Bước 4: Thiết kế Error Response Strategy**

**Cách suy luận error handling:**
```python
# 🤔 Error response nên chứa thông tin gì?

def _handle_auth_error(self, error: Exception, context: dict = None) -> ErrorResponse:
    """
    Suy luận error response requirements:

    🤔 Frontend cần gì khi có lỗi?
    1. User-friendly message để hiển thị
    2. Error code để handle programmatically
    3. Field-specific errors để highlight UI
    4. Debug info (development only)
    """

    if isinstance(error, EmailAlreadyExistsError):
        return ErrorResponse(
            message=error.detail,           # "Email already registered"
            error_code=error.error_code,    # "AUTH_002"
            error_details={                 # Help frontend highlight email field
                "field": "email",
                "value": context.get("email") if context else None
            }
        )

    elif isinstance(error, WeakPasswordError):
        return ErrorResponse(
            message=error.detail,           # "Password does not meet requirements"
            error_code=error.error_code,    # "AUTH_004"
            error_details={                 # Help frontend show password rules
                "field": "password",
                "requirements": [
                    "At least 8 characters",
                    "Contains uppercase letter",
                    "Contains lowercase letter",
                    "Contains number"
                ]
            }
        )

    else:
        # Generic error
        logger.error(f"Unexpected error: {str(error)}")
        return ErrorResponse(
            message="An unexpected error occurred",
            error_code="INTERNAL_ERROR"
        )
```

#### **Bước 5: Thiết kế HTTP Status Code Strategy**

**Cách suy luận status codes:**
```python
# 🤔 Khi nào dùng status code nào?

"""
Suy luận từ HTTP semantics:

200 OK:
- Successful GET, PUT operations
- Login successful

201 Created:
- Successful POST operations
- User registration successful

400 Bad Request:
- Validation errors
- Business rule violations (email exists, weak password)

401 Unauthorized:
- Authentication required
- Invalid credentials

403 Forbidden:
- User authenticated but not authorized
- Account deactivated

404 Not Found:
- Resource doesn't exist
- User not found

409 Conflict:
- Resource already exists
- Email already registered

500 Internal Server Error:
- Unexpected server errors
- Database connection issues
"""

async def register(self, user_data: UserCreate):
    try:
        user = await self.auth_service.create_user(user_data)
        return SuccessResponse(
            message="Registration successful",
            data=user_response
        ), 201  # 🤔 201 Created cho resource mới

    except EmailAlreadyExistsError as e:
        return ErrorResponse(
            message=e.detail,
            error_code=e.error_code
        ), 409  # 🤔 409 Conflict cho resource đã tồn tại

    except WeakPasswordError as e:
        return ErrorResponse(
            message=e.detail,
            error_code=e.error_code
        ), 400  # 🤔 400 Bad Request cho validation error
```

#### **Bước 6: Thiết kế Logging Strategy**

**Cách suy luận logging requirements:**
```python
# 🤔 Cần log thông tin gì để debug và monitor?

async def register(self, user_data: UserCreate):
    # Log request start
    logger.info(f"Registration attempt for email: {user_data.email}")

    try:
        user = await self.auth_service.create_user(user_data)

        # Log success
        logger.info(f"User registered successfully: {user.id}")

        return SuccessResponse(...)

    except EmailAlreadyExistsError as e:
        # Log business error (INFO level - expected)
        logger.info(f"Registration failed - email exists: {user_data.email}")
        return ErrorResponse(...)

    except Exception as e:
        # Log unexpected error (ERROR level - needs investigation)
        logger.error(f"Unexpected registration error for {user_data.email}: {str(e)}")
        return ErrorResponse(...)

# 🤔 Log levels strategy:
# INFO: Business events (registration, login)
# WARNING: Suspicious activities (multiple failed logins)
# ERROR: Unexpected errors (database issues, external service failures)
# DEBUG: Detailed flow info (development only)
```

### **🏗️ Cấu trúc AuthController**

```python
# backend/app/modules/auth/controllers/auth_controller.py

class AuthController:
    """
    Controller xử lý tất cả requests liên quan authentication

    Pattern: Thin Controller, Fat Service
    - Controller: Ít logic, chỉ điều phối
    - Service: Nhiều logic, xử lý nghiệp vụ
    """

    def __init__(self, db: AsyncSession):
        self.auth_service = AuthService(db)

    async def register(self, user_data: UserCreate) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        """
        Xử lý đăng ký user mới

        Flow:
        1. Gọi service để tạo user
        2. Convert User model → UserResponse schema
        3. Trả về SuccessResponse
        4. Nếu có lỗi → ErrorResponse
        """
        try:
            # Gọi service xử lý logic
            user = await self.auth_service.create_user(user_data)

            # Lấy user với profile info để tạo response
            user_with_profile = await self.auth_service.get_user_by_id_with_profile(user.id)

            # Tạo response object
            user_response = UserResponse(
                user_id=user_with_profile.id,
                email=user_with_profile.email,
                username=user_with_profile.username,
                created_at=user_with_profile.created_at,
                # Profile info (có thể None)
                full_name=user_with_profile.profile.full_name if user_with_profile.profile else None,
                phone=user_with_profile.profile.phone if user_with_profile.profile else None,
                avatar_url=user_with_profile.profile.avatar_url if user_with_profile.profile else None
            )

            return SuccessResponse(
                message=Messages.REGISTER_SUCCESS,
                data=user_response
            )

        # Handle specific exceptions
        except EmailAlreadyExistsError as e:
            return ErrorResponse(
                message=e.detail,
                error_code=e.error_code,
                error_details={"email": user_data.email}
            )

        except WeakPasswordError as e:
            return ErrorResponse(
                message=e.detail,
                error_code=e.error_code
            )

        except InvalidDataError as e:
            return ErrorResponse(
                message=e.detail,
                error_code=e.error_code,
                error_details={"validation_error": str(e)}
            )

        # Handle unexpected errors
        except Exception as e:
            logger.error(f"Unexpected error in register: {str(e)}")
            return ErrorResponse(
                message="Có lỗi xảy ra, vui lòng thử lại",
                error_code="INTERNAL_ERROR"
            )

    async def login(self, login_data: UserLogin) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        """
        Xử lý đăng nhập user

        Flow:
        1. Gọi service authenticate
        2. Tạo JWT token (TODO)
        3. Trả về user info + token
        """
        try:
            # Authenticate user
            user = await self.auth_service.authenticate_user(
                login_data.email,
                login_data.password
            )

            # Get user with profile
            user_with_profile = await self.auth_service.get_user_by_id_with_profile(user.id)

            # Create response
            user_response = UserResponse(
                user_id=user_with_profile.id,
                email=user_with_profile.email,
                username=user_with_profile.username,
                created_at=user_with_profile.created_at,
                full_name=user_with_profile.profile.full_name if user_with_profile.profile else None,
                phone=user_with_profile.profile.phone if user_with_profile.profile else None,
                avatar_url=user_with_profile.profile.avatar_url if user_with_profile.profile else None
            )

            return SuccessResponse(
                message=Messages.LOGIN_SUCCESS,
                data=user_response
            )

        except InvalidCredentialsError as e:
            return ErrorResponse(
                message=e.detail,
                error_code=e.error_code
            )

        except AccountDeactivatedError as e:
            return ErrorResponse(
                message=e.detail,
                error_code=e.error_code
            )

        except Exception as e:
            logger.error(f"Unexpected error in login: {str(e)}")
            return ErrorResponse(
                message="Có lỗi xảy ra, vui lòng thử lại",
                error_code="INTERNAL_ERROR"
            )
```

### **🔍 Controller Pattern Explained**

**🎯 Thin Controller Pattern:**
```python
# ✅ GOOD: Controller mỏng, chỉ điều phối
async def register(self, user_data):
    try:
        user = await self.service.create_user(user_data)  # Gọi service
        return SuccessResponse(data=user)                 # Tạo response
    except Exception as e:
        return ErrorResponse(message=str(e))              # Handle error

# ❌ BAD: Controller dày, chứa business logic
async def register(self, user_data):
    # 50 lines validation
    # 30 lines database operations
    # 20 lines business rules
    # Controller trở thành "God Class"
```

**🎯 Dependency Injection Pattern:**
```python
# Controller nhận database session từ bên ngoài
def __init__(self, db: AsyncSession):
    self.auth_service = AuthService(db)

# Lợi ích:
# - Dễ test (mock database)
# - Loose coupling
# - Flexible configuration
```

### **🎯 Error Handling Strategy**

```python
# Xử lý lỗi theo mức độ cụ thể → chung
try:
    # Business logic
    pass

except SpecificError as e:          # Lỗi cụ thể
    return ErrorResponse(...)

except AnotherSpecificError as e:   # Lỗi cụ thể khác
    return ErrorResponse(...)

except Exception as e:              # Lỗi chung (fallback)
    logger.error(f"Unexpected: {e}")
    return ErrorResponse(message="Internal error")
```

---

## 7. **ROUTES - API ENDPOINTS**

### **🎯 Routes là gì?**

Routes là **"cửa hàng"** của ứng dụng - nơi client giao tiếp với server:
- Định nghĩa URL endpoints
- Specify HTTP methods (GET, POST, PUT, DELETE)
- Validate request data
- Call controllers
- Return responses

### **🏗️ Cấu trúc Routes**

```python
# backend/app/modules/auth/routes/auth_routes.py

from fastapi import APIRouter, Depends, status
from sqlmodel.ext.asyncio.session import AsyncSession
from typing import Union

from app.shared.schemas.response import SuccessResponse, ErrorResponse
from app.modules.auth.schemas.user import UserCreate, UserLogin, UserResponse
from app.modules.auth.controllers.auth_controller import AuthController
from app.api.v1.deps import get_db

# Tạo router cho auth module
router = APIRouter(prefix="/auth", tags=["Authentication"])

async def get_auth_controller(db: AsyncSession = Depends(get_db)) -> AuthController:
    """
    Dependency function để tạo AuthController

    Tại sao cần function này?
    - FastAPI Dependency Injection system
    - Tự động inject database session
    - Mỗi request có controller riêng
    """
    return AuthController(db)

@router.post(
    "/register",
    response_model=Union[SuccessResponse[UserResponse], ErrorResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Đăng ký tài khoản mới",
    description="Tạo tài khoản user mới với username, email và password"
)
async def register(
    user_data: UserCreate,  # Request body - tự động validate
    controller: AuthController = Depends(get_auth_controller)  # Inject controller
):
    """
    API endpoint đăng ký user mới

    Request Body:
    {
        "username": "john_doe",
        "email": "<EMAIL>",
        "password": "Password123!"
    }

    Response Success (201):
    {
        "success": true,
        "message": "Đăng ký thành công",
        "data": {
            "user_id": "123e4567-e89b-12d3-a456-************",
            "username": "john_doe",
            "email": "<EMAIL>",
            "created_at": "2025-09-14T10:30:00Z",
            "full_name": null,
            "phone": null,
            "avatar_url": null
        },
        "timestamp": "2025-09-14T10:30:00Z"
    }

    Response Error (400):
    {
        "success": false,
        "message": "Email already registered",
        "error_code": "AUTH_002",
        "error_details": {"email": "<EMAIL>"},
        "timestamp": "2025-09-14T10:30:00Z"
    }
    """
    return await controller.register(user_data)

@router.post(
    "/login",
    response_model=Union[SuccessResponse[UserResponse], ErrorResponse],
    status_code=status.HTTP_200_OK,
    summary="Đăng nhập",
    description="Xác thực user với email và password"
)
async def login(
    login_data: UserLogin,
    controller: AuthController = Depends(get_auth_controller)
):
    """
    API endpoint đăng nhập

    Request Body:
    {
        "email": "<EMAIL>",
        "password": "Password123!"
    }

    Response Success (200):
    {
        "success": true,
        "message": "Đăng nhập thành công",
        "data": {
            "user_id": "123e4567-e89b-12d3-a456-************",
            "username": "john_doe",
            "email": "<EMAIL>",
            "created_at": "2025-09-14T10:30:00Z",
            "full_name": "John Doe Smith",
            "phone": "+***********",
            "avatar_url": "https://example.com/avatar.jpg"
        },
        "timestamp": "2025-09-14T10:30:00Z"
    }
    """
    return await controller.login(login_data)

@router.get(
    "/health",
    response_model=SuccessResponse[dict],
    summary="Kiểm tra tình trạng service",
    description="Health check endpoint để kiểm tra auth service"
)
async def health_check(
    controller: AuthController = Depends(get_auth_controller)
):
    """
    Health check endpoint

    Response (200):
    {
        "success": true,
        "message": "Auth service đang hoạt động bình thường",
        "data": {
            "service": "auth",
            "status": "healthy",
            "features": {
                "user_registration": true,
                "user_authentication": true,
                "jwt_tokens": false,
                "email_verification": false
            }
        }
    }
    """
    return await controller.health_check()
```

### **🔍 FastAPI Route Decorators Explained**

```python
@router.post(                                    # HTTP method
    "/register",                                 # URL path
    response_model=Union[SuccessResponse[UserResponse], ErrorResponse],  # Response type
    status_code=status.HTTP_201_CREATED,        # Default status code
    summary="Đăng ký tài khoản mới",            # API docs title
    description="Tạo tài khoản user mới..."     # API docs description
)
```

**Giải thích từng phần:**
- `@router.post`: HTTP POST method
- `"/register"`: URL sẽ là `/auth/register` (prefix + path)
- `response_model`: Type hint cho response, tự động generate OpenAPI docs
- `status_code`: HTTP status code mặc định khi success
- `summary`: Tiêu đề ngắn trong API docs
- `description`: Mô tả chi tiết trong API docs

### **🎯 Dependency Injection trong FastAPI**

```python
async def get_auth_controller(
    db: AsyncSession = Depends(get_db)  # Inject database session
) -> AuthController:
    return AuthController(db)

async def register(
    user_data: UserCreate,              # Request body
    controller: AuthController = Depends(get_auth_controller)  # Inject controller
):
    return await controller.register(user_data)
```

**Flow của Dependency Injection:**
1. Client gửi request đến `/auth/register`
2. FastAPI gọi `get_db()` để tạo database session
3. FastAPI gọi `get_auth_controller(db)` với session từ step 2
4. FastAPI gọi `register(user_data, controller)` với controller từ step 3
5. Route function thực thi và trả về response

**Lợi ích:**
- ✅ Automatic resource management (database connection)
- ✅ Easy testing (mock dependencies)
- ✅ Clean separation of concerns
- ✅ Reusable components

### **🌐 API Router Structure**

```python
# backend/app/api/v1/api.py

from fastapi import APIRouter
from app.modules.auth.routes.auth_routes import router as auth_router
from app.modules.auth.routes.profile_routes import router as profile_router

# Main API router
router = APIRouter()

# Include module routers
router.include_router(auth_router)      # /auth/*
router.include_router(profile_router)   # /profile/*

# Kết quả:
# /api/v1/auth/register
# /api/v1/auth/login
# /api/v1/profile/update
# /api/v1/profile/me
```

### **📋 Route Best Practices**

1. **RESTful URLs**: Sử dụng chuẩn REST
   ```python
   GET    /users          # Lấy danh sách users
   GET    /users/{id}     # Lấy user cụ thể
   POST   /users          # Tạo user mới
   PUT    /users/{id}     # Cập nhật user
   DELETE /users/{id}     # Xóa user
   ```

2. **Meaningful HTTP Status Codes**:
   ```python
   200 OK           # Success (GET, PUT)
   201 Created      # Success (POST)
   400 Bad Request  # Client error (validation)
   401 Unauthorized # Authentication required
   403 Forbidden    # Permission denied
   404 Not Found    # Resource not found
   500 Internal     # Server error
   ```

3. **Consistent Response Format**:
   ```python
   # Tất cả APIs đều trả về format này
   {
       "success": true/false,
       "message": "...",
       "data": {...},
       "timestamp": "..."
   }
   ```

---

## 8. **AUTHENTICATION & JWT**

### **🎯 JWT là gì?**

JWT (JSON Web Token) là **"thẻ căn cước điện tử"** cho user:
- Chứa thông tin user (user_id, email, permissions)
- Được mã hóa và ký số
- Client gửi kèm mỗi request để xác thực
- Server verify token để biết user là ai

### **🏗️ JWT Structure**

```
JWT = Header.Payload.Signature

Header:    {"alg": "HS256", "typ": "JWT"}
Payload:   {"user_id": "123", "email": "<EMAIL>", "exp": 1640995200}
Signature: HMACSHA256(base64UrlEncode(header) + "." + base64UrlEncode(payload), secret)

Kết quả:   eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.signature
```

### **🔐 JWT Implementation**

```python
# backend/app/core/security.py

from datetime import datetime, timedelta, timezone
from typing import Optional
import jwt
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = "your-secret-key-here"  # Từ environment variables
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def hash_password(password: str) -> str:
    """Hash password với bcrypt"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password với hash"""
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Tạo JWT access token

    Args:
        data: Payload data (user_id, email, etc.)
        expires_delta: Thời gian hết hạn (default: 30 minutes)

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    # Set expiration time
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})

    # Create JWT token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    Verify và decode JWT token

    Args:
        token: JWT token string

    Returns:
        Payload dict nếu valid, None nếu invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None  # Token hết hạn
    except jwt.JWTError:
        return None  # Token không hợp lệ
```

### **🔗 JWT Integration với Auth System**

```python
# Cập nhật AuthController để tạo JWT token

async def login(self, login_data: UserLogin) -> Union[SuccessResponse[dict], ErrorResponse]:
    """Đăng nhập và trả về JWT token"""
    try:
        # Authenticate user
        user = await self.auth_service.authenticate_user(
            login_data.email,
            login_data.password
        )

        # Create JWT token
        token_data = {
            "user_id": str(user.id),
            "email": user.email,
            "username": user.username
        }
        access_token = create_access_token(data=token_data)

        # Get user with profile
        user_with_profile = await self.auth_service.get_user_by_id_with_profile(user.id)

        # Create response với token
        return SuccessResponse(
            message=Messages.LOGIN_SUCCESS,
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # seconds
                "user": {
                    "user_id": user_with_profile.id,
                    "username": user_with_profile.username,
                    "email": user_with_profile.email,
                    "full_name": user_with_profile.profile.full_name if user_with_profile.profile else None
                }
            }
        )

    except Exception as e:
        return ErrorResponse(message=str(e))
```

### **🛡️ Protected Routes với JWT**

```python
# backend/app/api/v1/deps.py

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.core.security import verify_token
from app.modules.auth.services.auth import AuthService

# JWT Bearer scheme
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Dependency để lấy current user từ JWT token

    Flow:
    1. Extract token từ Authorization header
    2. Verify token
    3. Get user từ database
    4. Return user object
    """

    # Extract token
    token = credentials.credentials

    # Verify token
    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Get user from database
    user_id = payload.get("user_id")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )

    auth_service = AuthService(db)
    user = await auth_service.get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )

    return user

# Sử dụng trong protected routes
@router.get("/profile/me")
async def get_my_profile(
    current_user: User = Depends(get_current_user)  # Require authentication
):
    """Protected route - cần JWT token"""
    return SuccessResponse(
        message="Profile retrieved successfully",
        data={
            "user_id": current_user.id,
            "username": current_user.username,
            "email": current_user.email
        }
    )
```

### **📱 Client-side JWT Usage**

```javascript
// Frontend JavaScript example

// 1. Login và lưu token
const loginResponse = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Password123!'
    })
});

const loginData = await loginResponse.json();
if (loginData.success) {
    // Lưu token vào localStorage
    localStorage.setItem('access_token', loginData.data.access_token);
}

// 2. Sử dụng token cho protected requests
const token = localStorage.getItem('access_token');
const profileResponse = await fetch('/api/v1/profile/me', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});
```

---

## 9. **DATABASE MIGRATION**

### **🎯 Migration là gì?**

Migration là **"kịch bản thay đổi database"**:
- Tạo/xóa tables
- Thêm/xóa columns
- Thay đổi data types
- Tạo indexes
- Migrate data

### **🔄 Hai cách Migration**

#### **Cách 1: Xóa DB và tạo lại (Development)**
```python
# Trong development, có thể xóa hết và tạo lại
# SQLModel sẽ tự động tạo tables từ models

# backend/app/core/database.py
async def init_db():
    """Tạo tất cả tables từ SQLModel"""
    async with engine.begin() as conn:
        # Drop all tables (chỉ dùng trong development!)
        await conn.run_sync(SQLModel.metadata.drop_all)

        # Create all tables
        await conn.run_sync(SQLModel.metadata.create_all)
```

#### **Cách 2: Migration Scripts (Production)**
```sql
-- backend/migrations/001_add_username_to_users.sql

-- Add username column to users table
ALTER TABLE users
ADD COLUMN username VARCHAR(50) UNIQUE;

-- Create index on username for better performance
CREATE INDEX idx_users_username ON users(username);

-- Update existing users to have username based on email (temporary)
UPDATE users
SET username = SPLIT_PART(email, '@', 1)
WHERE username IS NULL;

-- Make username NOT NULL after updating existing records
ALTER TABLE users
ALTER COLUMN username SET NOT NULL;

-- Add full_name column to user_profiles table
ALTER TABLE user_profiles
ADD COLUMN full_name VARCHAR(255);

-- Create index on full_name for better search performance
CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);
```

### **🐍 Python Migration Script**

```python
# backend/run_migration.py

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def run_migration():
    """Chạy migration script"""

    # Database connection
    DATABASE_URL = os.getenv("DATABASE_URL")

    try:
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected to database")

        # Migration commands
        commands = [
            "ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE;",
            "CREATE INDEX idx_users_username ON users(username);",
            "UPDATE users SET username = SPLIT_PART(email, '@', 1) WHERE username IS NULL;",
            "ALTER TABLE users ALTER COLUMN username SET NOT NULL;",
            "ALTER TABLE user_profiles ADD COLUMN full_name VARCHAR(255);",
            "CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);"
        ]

        for i, command in enumerate(commands, 1):
            try:
                await conn.execute(command)
                print(f"✅ Command {i}/{len(commands)}: Success")
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"⚠️  Command {i}: Already exists")
                else:
                    print(f"❌ Command {i}: {str(e)}")
                    raise

        print("🎉 Migration completed!")

    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(run_migration())
```

### **🚀 Chạy Migration**

```bash
# Cách 1: Xóa DB và chạy server (Development)
# Server sẽ tự động tạo lại tables
uvicorn app.main:app --reload

# Cách 2: Chạy migration script
cd backend
python run_migration.py

# Cách 3: Sử dụng psql (nếu có PostgreSQL CLI)
psql -d lskinaid -f migrations/001_add_username_to_users.sql
```

---

## 10. **TESTING & DEBUGGING**

### **🎯 Testing Strategy**

#### **Unit Tests - Test từng component riêng biệt**
```python
# tests/test_auth_service.py

import pytest
from app.modules.auth.services.auth import AuthService
from app.modules.auth.schemas.user import UserCreate

@pytest.mark.asyncio
async def test_create_user_success():
    """Test tạo user thành công"""
    # Arrange
    user_data = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="Password123!"
    )

    # Act
    user = await auth_service.create_user(user_data)

    # Assert
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.is_active == True

@pytest.mark.asyncio
async def test_create_user_duplicate_email():
    """Test tạo user với email đã tồn tại"""
    # Arrange
    user_data = UserCreate(
        username="testuser2",
        email="<EMAIL>",  # Email đã tồn tại
        password="Password123!"
    )

    # Act & Assert
    with pytest.raises(EmailAlreadyExistsError):
        await auth_service.create_user(user_data)
```

#### **Integration Tests - Test API endpoints**
```python
# tests/test_auth_routes.py

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_register_success():
    """Test API đăng ký thành công"""
    response = client.post("/api/v1/auth/register", json={
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "Password123!"
    })

    assert response.status_code == 201
    data = response.json()
    assert data["success"] == True
    assert data["data"]["username"] == "newuser"

def test_register_duplicate_email():
    """Test API đăng ký với email trùng"""
    response = client.post("/api/v1/auth/register", json={
        "username": "anotheruser",
        "email": "<EMAIL>",  # Email đã tồn tại
        "password": "Password123!"
    })

    assert response.status_code == 400
    data = response.json()
    assert data["success"] == False
    assert data["error_code"] == "AUTH_002"
```

### **🐛 Debugging Techniques**

#### **1. Logging**
```python
import logging

logger = logging.getLogger(__name__)

async def create_user(self, user_data: UserCreate):
    logger.info(f"Creating user with email: {user_data.email}")

    try:
        # Business logic
        user = User(...)
        logger.info(f"User created successfully: {user.id}")
        return user

    except Exception as e:
        logger.error(f"Failed to create user: {str(e)}")
        raise
```

#### **2. FastAPI Debug Mode**
```python
# main.py
app = FastAPI(debug=True)  # Enable debug mode

# Hoặc chạy với debug
uvicorn app.main:app --reload --log-level debug
```

#### **3. Database Query Debugging**
```python
# Enable SQL logging
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# Sẽ in ra tất cả SQL queries
```

#### **4. API Testing với Postman/curl**
```bash
# Test register API
curl -X POST "http://localhost:8000/api/v1/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "testuser",
       "email": "<EMAIL>",
       "password": "Password123!"
     }'

# Test login API
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "Password123!"
     }'
```

---

## 🎯 **DESIGN THINKING PROCESS - TÓM TẮT**

### **🧠 Quy trình suy luận thiết kế hệ thống:**

#### **1. Requirements Analysis (Phân tích yêu cầu)**
```
🤔 Câu hỏi cốt lõi:
- User muốn làm gì? (User Stories)
- System cần lưu trữ gì? (Data Requirements)
- Các thành phần tương tác như thế nào? (System Architecture)
- Lỗi gì có thể xảy ra? (Error Scenarios)
- Performance requirements? (Scale, Speed)
```

#### **2. Domain Modeling (Mô hình hóa domain)**
```
🤔 Từ requirements → Entities:
User Story: "Tôi muốn đăng ký tài khoản"
→ Cần User entity (username, email, password)
→ Cần UserProfile entity (personal info)
→ Cần Authentication logic
→ Cần Validation rules
```

#### **3. API Design (Thiết kế API)**
```
🤔 Từ User Stories → API Endpoints:
"Đăng ký tài khoản" → POST /auth/register
"Đăng nhập" → POST /auth/login
"Cập nhật profile" → PUT /profile/update
"Xem profile" → GET /profile/me
```

#### **4. Error Scenarios (Kịch bản lỗi)**
```
🤔 Từ mỗi operation → Possible errors:
Register → EmailExists, WeakPassword, InvalidFormat
Login → InvalidCredentials, AccountDeactivated
Update → ValidationError, NotFound, Unauthorized
```

#### **5. Data Flow Design (Thiết kế luồng dữ liệu)**
```
🤔 Request → Response flow:
Client Request → Routes → Controllers → Services → Database
                   ↓         ↓          ↓         ↓
                Validate   Handle     Business   Store
                Format     Errors     Logic      Data
```

### **🎯 Design Principles đã áp dụng:**

1. **Single Responsibility**: Mỗi class/method có 1 nhiệm vụ rõ ràng
2. **Separation of Concerns**: Tách biệt presentation, business, data layers
3. **Dependency Inversion**: High-level modules không phụ thuộc low-level modules
4. **Open/Closed**: Mở cho extension, đóng cho modification
5. **DRY (Don't Repeat Yourself)**: Tránh duplicate code
6. **KISS (Keep It Simple, Stupid)**: Giữ đơn giản, dễ hiểu

### **📚 Tư duy thiết kế từng layer:**

#### **Models (Database Layer)**
```
🤔 Suy luận: Dữ liệu cần lưu trữ như thế nào?
→ Entities, Relationships, Constraints, Indexes
→ Performance, Scalability, Data integrity
```

#### **Schemas (API Layer)**
```
🤔 Suy luận: Client và Server trao đổi dữ liệu như thế nào?
→ Input validation, Output format, Error responses
→ Type safety, Documentation, Consistency
```

#### **Services (Business Layer)**
```
🤔 Suy luận: Business logic phức tạp như thế nào?
→ Operations, Validations, Transformations, External calls
→ Testability, Reusability, Maintainability
```

#### **Controllers (Presentation Layer)**
```
🤔 Suy luận: HTTP requests/responses được xử lý như thế nào?
→ Request handling, Response formatting, Error mapping
→ Thin controllers, Consistent responses, Proper status codes
```

#### **Routes (API Gateway)**
```
🤔 Suy luận: API endpoints được expose như thế nào?
→ URL design, HTTP methods, Documentation, Security
→ RESTful design, Versioning, Rate limiting
```

---

## 🎯 **TÓM TẮT & NEXT STEPS**

### **✅ Những gì đã học:**

1. **Architecture**: Layered architecture với Models, Schemas, Services, Controllers, Routes
2. **Database**: SQLModel với relationships, migrations
3. **API Design**: RESTful APIs với chuẩn response format
4. **Error Handling**: Custom exceptions với error codes
5. **Authentication**: JWT tokens cho security
6. **Testing**: Unit tests và integration tests
7. **🧠 Design Thinking**: Quy trình suy luận và thiết kế hệ thống từ A-Z

### **🚀 Bước tiếp theo:**

1. **Chạy migration**: Cập nhật database schema
2. **Test APIs**: Sử dụng Postman test các endpoints
3. **Implement JWT**: Hoàn thiện authentication system
4. **Add more features**: Profile management, file upload
5. **Write tests**: Unit tests và integration tests
6. **Deploy**: Chuẩn bị deploy lên production

### **📚 Tài liệu tham khảo:**

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLModel Documentation](https://sqlmodel.tiangolo.com/)
- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

**Chúc bạn thành công với dự án SkinAid! 🎉**
