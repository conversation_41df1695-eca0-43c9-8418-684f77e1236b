# ⚡ LESSON 08: PERFORMANCE & SCALING - HIỆU SUẤT VÀ MỞ RỘNG

**<PERSON><PERSON>c tiêu**: Optimize JWT system cho high performance và scalability

---

## 🎯 **PHẦN 1: PERFORMANCE BOTTLENECKS - ĐIỂM NGHẼN HIỆU SUẤT**

### **🔍 JWT Performance Analysis**

```python
# performance/profiler.py - JWT performance profiling
import time
import asyncio
from functools import wraps
from typing import Dict, List
import statistics

class JWTPerformanceProfiler:
    """Profile JWT operations để identify bottlenecks"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.operation_counts: Dict[str, int] = {}
    """
    Giải thích Performance Tracking:
    - metrics: Store execution times cho each operation
    - operation_counts: Track frequency of operations
    - Used để identify performance bottlenecks
    """
    
    def profile_operation(self, operation_name: str):
        """
        Decorator để profile JWT operations
        
        Args:
            operation_name: Name of operation being profiled
        """
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time
                    self._record_metric(operation_name, execution_time)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time
                    self._record_metric(operation_name, execution_time)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
        """
        Giải thích Performance Profiling:
        1. Record start time before operation
        2. Execute function
        3. Record end time after operation
        4. Calculate execution time
        5. Store metrics cho analysis
        """
    
    def _record_metric(self, operation_name: str, execution_time: float):
        """Record performance metric"""
        if operation_name not in self.metrics:
            self.metrics[operation_name] = []
            self.operation_counts[operation_name] = 0
        
        self.metrics[operation_name].append(execution_time)
        self.operation_counts[operation_name] += 1
    
    def get_performance_report(self) -> Dict:
        """
        Generate performance report
        
        Returns:
            Dict với performance statistics
        """
        report = {}
        for operation, times in self.metrics.items():
            if times:
                report[operation] = {
                    "count": self.operation_counts[operation],
                    "avg_time": statistics.mean(times),
                    "min_time": min(times),
                    "max_time": max(times),
                    "p95_time": statistics.quantiles(times, n=20)[18] if len(times) > 20 else max(times),
                    "total_time": sum(times)
                }
        return report
        """
        Giải thích Performance Metrics:
        - count: Number of operations
        - avg_time: Average execution time
        - min/max_time: Best/worst case performance
        - p95_time: 95th percentile (excludes outliers)
        - total_time: Cumulative time spent
        """

# Global profiler instance
jwt_profiler = JWTPerformanceProfiler()

# Common bottleneck operations
@jwt_profiler.profile_operation("jwt_decode")
def profile_jwt_decode(token: str):
    """Profile JWT decoding performance"""
    return jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

@jwt_profiler.profile_operation("password_verify")
def profile_password_verify(password: str, hashed: str):
    """Profile password verification performance"""
    return pwd_context.verify(password, hashed)

@jwt_profiler.profile_operation("database_user_lookup")
async def profile_user_lookup(user_id: str):
    """Profile database user lookup performance"""
    return await get_user_by_id(user_id)
```

### **📊 Performance Bottleneck Analysis**

```python
# Common JWT performance bottlenecks:
"""
1. JWT SIGNATURE VERIFICATION (CPU-intensive):
   - HMAC-SHA256 computation
   - RSA signature verification (slower)
   - Multiple verifications per request

2. PASSWORD HASHING (CPU-intensive):
   - bcrypt intentionally slow (10-12 rounds)
   - Blocks event loop trong sync operations
   - Memory intensive

3. DATABASE LOOKUPS (I/O-intensive):
   - User profile queries
   - Permission lookups
   - Refresh token validation
   - Session data retrieval

4. CRYPTOGRAPHIC OPERATIONS:
   - Random token generation
   - Hash computations
   - Encryption/decryption

5. NETWORK LATENCY:
   - Database connections
   - External service calls
   - Cache misses
"""

# Performance impact analysis:
"""
Operation Performance (typical times):
- JWT decode: 0.1-0.5ms
- Password verify: 50-100ms (bcrypt)
- Database query: 1-10ms
- Redis cache: 0.1-1ms
- Token generation: 0.1-1ms

Bottleneck Priority:
1. Password hashing (highest impact)
2. Database queries (medium impact)
3. JWT operations (low impact)
"""
```

## 🚀 **PHẦN 2: CACHING STRATEGIES - CHIẾN LƯỢC CACHE**

### **⚡ Redis Caching Implementation**

```python
# cache/redis_cache.py - Redis caching cho JWT operations
import redis.asyncio as redis
import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta

class JWTRedisCache:
    """Redis caching cho JWT-related operations"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_pool = redis.ConnectionPool.from_url(redis_url)
        self.redis_client = redis.Redis(connection_pool=self.redis_pool)
        
        # Cache TTL settings
        self.user_cache_ttl = 300      # 5 minutes
        self.permission_cache_ttl = 600 # 10 minutes
        self.token_cache_ttl = 900     # 15 minutes (access token lifetime)
    """
    Giải thích Redis Cache Settings:
    - redis_pool: Connection pooling cho performance
    - Different TTLs cho different data types
    - user_cache_ttl: User profile data
    - permission_cache_ttl: Role permissions
    - token_cache_ttl: Decoded token payloads
    """
    
    async def cache_user_data(self, user_id: str, user_data: Dict, ttl: Optional[int] = None):
        """
        Cache user data trong Redis
        
        Args:
            user_id: User identifier
            user_data: User data to cache
            ttl: Time to live in seconds
        """
        cache_key = f"user:{user_id}"
        ttl = ttl or self.user_cache_ttl
        
        # Serialize user data
        serialized_data = json.dumps(user_data, default=str)
        
        await self.redis_client.setex(cache_key, ttl, serialized_data)
        """
        Giải thích User Data Caching:
        1. Create unique cache key
        2. Serialize data to JSON
        3. Set với expiration time
        4. default=str handles datetime objects
        """
    
    async def get_cached_user_data(self, user_id: str) -> Optional[Dict]:
        """
        Get cached user data
        
        Args:
            user_id: User identifier
        
        Returns:
            Dict với user data hoặc None
        """
        cache_key = f"user:{user_id}"
        cached_data = await self.redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        return None
        """
        Giải thích Cache Retrieval:
        1. Get data từ Redis
        2. Deserialize JSON if exists
        3. Return None if cache miss
        """
    
    async def cache_token_payload(self, token_hash: str, payload: Dict, ttl: Optional[int] = None):
        """
        Cache decoded JWT payload
        
        Args:
            token_hash: Hash of JWT token
            payload: Decoded payload
            ttl: Time to live
        """
        cache_key = f"token:{token_hash}"
        ttl = ttl or self.token_cache_ttl
        
        # Use pickle cho complex objects
        serialized_payload = pickle.dumps(payload)
        
        await self.redis_client.setex(cache_key, ttl, serialized_payload)
        """
        Giải thích Token Payload Caching:
        - Hash token để create cache key
        - Use pickle cho complex Python objects
        - TTL matches access token lifetime
        - Avoids repeated JWT decoding
        """
    
    async def get_cached_token_payload(self, token_hash: str) -> Optional[Dict]:
        """Get cached token payload"""
        cache_key = f"token:{token_hash}"
        cached_payload = await self.redis_client.get(cache_key)
        
        if cached_payload:
            return pickle.loads(cached_payload)
        return None
    
    async def cache_user_permissions(self, user_id: str, permissions: List[str], ttl: Optional[int] = None):
        """
        Cache user permissions
        
        Args:
            user_id: User identifier
            permissions: List of permissions
            ttl: Time to live
        """
        cache_key = f"permissions:{user_id}"
        ttl = ttl or self.permission_cache_ttl
        
        # Store as JSON array
        permissions_json = json.dumps(permissions)
        await self.redis_client.setex(cache_key, ttl, permissions_json)
        """
        Giải thích Permission Caching:
        - Separate cache key cho permissions
        - Longer TTL (permissions change less frequently)
        - JSON array format
        - Reduces database permission lookups
        """
    
    async def invalidate_user_cache(self, user_id: str):
        """
        Invalidate all cached data cho user
        
        Args:
            user_id: User identifier
        """
        keys_to_delete = [
            f"user:{user_id}",
            f"permissions:{user_id}"
        ]
        
        await self.redis_client.delete(*keys_to_delete)
        """
        Giải thích Cache Invalidation:
        - Delete multiple related keys
        - Called when user data changes
        - Ensures cache consistency
        - Prevents stale data issues
        """

# Global cache instance
jwt_cache = JWTRedisCache()
```

### **🔄 Cache-Aware JWT Operations**

```python
# auth/cached_operations.py - Cache-optimized JWT operations
import hashlib

class CachedJWTOperations:
    """JWT operations với intelligent caching"""
    
    def __init__(self, cache: JWTRedisCache):
        self.cache = cache
    
    async def get_user_with_cache(self, user_id: str) -> Optional[Dict]:
        """
        Get user data với caching
        
        Args:
            user_id: User identifier
        
        Returns:
            User data dict
        """
        # Try cache first
        cached_user = await self.cache.get_cached_user_data(user_id)
        if cached_user:
            return cached_user
        
        # Cache miss - get từ database
        user_data = await get_user_by_id(user_id)
        if user_data:
            # Cache cho future requests
            await self.cache.cache_user_data(user_id, user_data)
        
        return user_data
        """
        Giải thích Cache-First Strategy:
        1. Check cache first (fastest)
        2. Database lookup on cache miss
        3. Populate cache với fresh data
        4. Return data to caller
        """
    
    async def verify_token_with_cache(self, token: str) -> Optional[Dict]:
        """
        Verify JWT token với payload caching
        
        Args:
            token: JWT token string
        
        Returns:
            Token payload hoặc None
        """
        # Create token hash cho cache key
        token_hash = hashlib.sha256(token.encode()).hexdigest()[:16]
        
        # Try cached payload first
        cached_payload = await self.cache.get_cached_token_payload(token_hash)
        if cached_payload:
            # Verify expiration (cache might be stale)
            if cached_payload.get("exp", 0) > time.time():
                return cached_payload
            else:
                # Expired token, remove từ cache
                await self.cache.redis_client.delete(f"token:{token_hash}")
        
        # Cache miss hoặc expired - verify token
        payload = verify_token(token)
        if payload:
            # Cache valid payload
            await self.cache.cache_token_payload(token_hash, payload)
        
        return payload
        """
        Giải thích Token Verification Caching:
        1. Hash token để create cache key
        2. Check cached payload first
        3. Verify expiration even for cached data
        4. Clean up expired cached tokens
        5. Cache fresh verification results
        """
    
    async def get_user_permissions_with_cache(self, user_id: str, role: str) -> List[str]:
        """
        Get user permissions với caching
        
        Args:
            user_id: User identifier
            role: User role
        
        Returns:
            List of permissions
        """
        # Try cache first
        cached_permissions = await self.cache.get_cached_user_permissions(user_id)
        if cached_permissions:
            return cached_permissions
        
        # Cache miss - compute permissions
        permissions = get_role_permissions(UserRole(role))
        permission_strings = [perm.value for perm in permissions]
        
        # Cache permissions
        await self.cache.cache_user_permissions(user_id, permission_strings)
        
        return permission_strings
        """
        Giải thích Permission Caching:
        1. Check permission cache
        2. Compute permissions từ role
        3. Convert to string format
        4. Cache computed permissions
        5. Return permission list
        """

# Global cached operations
cached_jwt_ops = CachedJWTOperations(jwt_cache)
```

## 🏗️ **PHẦN 3: SCALABILITY PATTERNS - MẪU MỞ RỘNG**

### **⚖️ Load Balancing Considerations**

```python
# scalability/load_balancing.py - Load balancing cho JWT systems
from typing import Dict, List
import consistent_hashing

class JWTLoadBalancingStrategy:
    """Strategies cho load balancing JWT systems"""
    
    def __init__(self):
        self.sticky_session_enabled = False
        self.token_affinity_enabled = True
    """
    Giải thích Load Balancing Options:
    - sticky_session_enabled: Route user to same server
    - token_affinity_enabled: Route based on token hash
    - JWT systems generally don't need sticky sessions
    """
    
    def should_use_sticky_sessions(self) -> bool:
        """
        Determine if sticky sessions needed
        
        Returns:
            bool: False cho JWT (stateless)
        """
        return False
        """
        Giải thích Sticky Sessions:
        - Traditional sessions need sticky routing
        - JWT tokens are stateless
        - Can be verified by any server
        - No need cho sticky sessions
        """
    
    def get_server_affinity_key(self, token: str) -> str:
        """
        Get server affinity key cho token
        
        Args:
            token: JWT token
        
        Returns:
            str: Affinity key
        """
        # Use token hash cho consistent routing
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash[:8]  # First 8 chars
        """
        Giải thích Token Affinity:
        - Hash token để get consistent key
        - Same token always routes to same server
        - Useful cho caching optimization
        - Optional optimization, not required
        """
    
    def distribute_cache_load(self, user_id: str, cache_servers: List[str]) -> str:
        """
        Distribute cache load across servers
        
        Args:
            user_id: User identifier
            cache_servers: Available cache servers
        
        Returns:
            str: Selected cache server
        """
        # Use consistent hashing
        ring = consistent_hashing.ConsistentHashRing(cache_servers)
        return ring.get_node(user_id)
        """
        Giải thích Consistent Hashing:
        - Distribute cache load evenly
        - Minimize cache misses during scaling
        - Handle server additions/removals gracefully
        - Better than simple modulo hashing
        """

class JWTHorizontalScaling:
    """Horizontal scaling patterns cho JWT systems"""
    
    def __init__(self):
        self.auto_scaling_enabled = True
        self.scale_up_threshold = 80    # CPU percentage
        self.scale_down_threshold = 20  # CPU percentage
    
    def calculate_required_instances(self, current_load: Dict) -> int:
        """
        Calculate required instances based on load
        
        Args:
            current_load: Current system load metrics
        
        Returns:
            int: Required number of instances
        """
        cpu_usage = current_load.get("cpu_percentage", 0)
        memory_usage = current_load.get("memory_percentage", 0)
        request_rate = current_load.get("requests_per_second", 0)
        
        # Calculate scaling factors
        cpu_factor = cpu_usage / 70  # Target 70% CPU
        memory_factor = memory_usage / 80  # Target 80% memory
        request_factor = request_rate / 1000  # Target 1000 RPS per instance
        
        # Use highest factor
        scaling_factor = max(cpu_factor, memory_factor, request_factor)
        
        return max(1, int(scaling_factor))
        """
        Giải thích Auto Scaling Logic:
        1. Monitor multiple metrics
        2. Calculate scaling factor cho each
        3. Use highest factor (bottleneck)
        4. Ensure minimum 1 instance
        5. Scale based on target utilization
        """
```

## 🎯 **LESSON 08 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Performance Optimizations:**
- **Performance Profiling**: Identify và measure bottlenecks
- **Redis Caching**: Multi-layer caching strategy
- **Cache-Aware Operations**: Intelligent cache usage
- **Load Balancing**: Stateless scaling strategies
- **Horizontal Scaling**: Auto-scaling based on metrics

### **🧠 Critical Performance Understanding:**
- **Bottleneck Identification**: Password hashing > DB queries > JWT ops
- **Caching Strategy**: Cache frequently accessed data với appropriate TTLs
- **Stateless Benefits**: Easy horizontal scaling without sticky sessions
- **Performance Monitoring**: Continuous measurement và optimization

### **🔧 Production Performance Considerations:**
- **Database Optimization**: Proper indexing, connection pooling
- **Cache Sizing**: Appropriate Redis memory allocation
- **Monitoring**: Real-time performance metrics
- **Auto-scaling**: Responsive to load changes

### **🎯 Next Steps:**
- **Lesson 09**: Compliance & Monitoring
- **Practice**: Load test optimized system
- **Monitoring**: Set up performance dashboards

**Bạn đã có high-performance JWT system! Ready for compliance?** 🚀
