#!/usr/bin/env python3
"""
Simple API test script to verify backend functionality after exception refactoring
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_endpoint(method, endpoint, data=None, description=""):
    """Test a single API endpoint"""
    print(f"\n🧪 Testing: {description}")
    print(f"   {method} {BASE_URL}{endpoint}")
    print("-" * 50)
    
    try:
        if method == "GET":
            response = requests.get(f"{BASE_URL}{endpoint}")
        elif method == "POST":
            response = requests.post(f"{BASE_URL}{endpoint}", json=data)
        elif method == "PUT":
            response = requests.put(f"{BASE_URL}{endpoint}", json=data)
        else:
            print(f"❌ Unsupported method: {method}")
            return
            
        print(f"✅ Status Code: {response.status_code}")
        
        try:
            response_json = response.json()
            print(f"📄 Response: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except:
            print(f"📄 Response (text): {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server không chạy hoặc không thể kết nối")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    print("🚀 SkinAid API Testing - Exception Refactoring Verification")
    print("=" * 60)
    
    # Test basic endpoints
    test_endpoint("GET", "/", description="Root Endpoint")
    test_endpoint("GET", "/api/v1/auth/health", description="Auth Health Check")
    test_endpoint("GET", "/api/v1/profile/me", description="Get Profile Me")
    
    # Test auth endpoints - new registration without username
    test_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "confirm_password": "TestPassword123!"
    }
    test_endpoint("POST", "/api/v1/auth/register", test_data, "Register New User (No Username)")

    # Test password mismatch
    mismatch_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "confirm_password": "DifferentPassword123!"
    }
    test_endpoint("POST", "/api/v1/auth/register", mismatch_data, "Register User (Password Mismatch)")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    test_endpoint("POST", "/api/v1/auth/login", login_data, "Login User (Expected: Verification required)")

    # Test email verification endpoint
    verify_data = {
        "email": "<EMAIL>",
        "token": "fake-token-for-testing"
    }
    test_endpoint("POST", "/api/v1/auth/verify-email", verify_data, "Verify Email (Expected: Invalid token)")
    
    # Test profile update
    profile_data = {
        "full_name": "Test User Updated",
        "phone": "0987654321",
        "address": "456 Updated Street"
    }
    test_endpoint("PUT", "/api/v1/profile/update", profile_data, "Update Profile")
    
    print("\n" + "=" * 60)
    print("🏁 Testing completed!")

if __name__ == "__main__":
    main()
