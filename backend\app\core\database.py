from sqlmodel import SQLModel
from sqlalchemy.ext.asyncio import AsyncE<PERSON><PERSON>, create_async_engine, async_sessionmaker, AsyncSession
from app.core.config import settings
from typing import AsyncGenerator 
from contextlib import asynccontextmanager

async_engine: AsyncEngine = create_async_engine(
    url = settings.DATABASE_URL, 
    echo=True, 
    future=True,
    pool_pre_ping=True,
)

async_session_maker = async_sessionmaker(
    bind= async_engine, 
    autoflush= False, 
    autocommit = False, 
    expire_on_commit= False, 
    class_= AsyncSession
)

# kết nối database đa ngôn ngữ 
@asynccontextmanager
async def get_db(): 
    session: AsyncSession = async_session_maker()
    try: 
        yield session
        await session.commit()
    except: 
        await session.rollback()
        raise
    finally: 
        await session.close() 

# kết nối database cho fastapi 
async def get_session(): 
    async with async_session_maker() as session: 
        yield session 

async def init_db(): 
    from app.modules.auth.models.user import User
    from app.modules.profile.models.user_profile import UserProfile
    
    async with async_engine.begin() as conn: 
        await conn.run_sync(SQLModel.metadata.create_all)

