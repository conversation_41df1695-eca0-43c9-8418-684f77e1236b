-- Migration: Remove username field and add email verification system
-- Date: 2024-12-15
-- Description: 
-- 1. Remove username column from users table
-- 2. Create verification_tokens table for email verification
-- 3. Update existing users to have is_verified = true (for existing data)

-- Step 1: Create verification_tokens table
CREATE TABLE IF NOT EXISTS verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_verification_tokens_user_id ON verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_email ON verification_tokens(email);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_token ON verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_expires_at ON verification_tokens(expires_at);

-- Step 2: Update existing users to be verified (for backward compatibility)
UPDATE users SET is_verified = TRUE WHERE is_verified = FALSE;

-- Step 3: Remove username column from users table
-- Note: Be careful with this step in production - backup data first!
-- ALTER TABLE users DROP COLUMN IF EXISTS username;

-- For now, we'll comment out the DROP COLUMN to be safe
-- Uncomment the line above when you're ready to permanently remove username

-- Step 4: Add constraint to ensure email is unique (if not already)
-- This should already exist, but adding for safety
-- ALTER TABLE users ADD CONSTRAINT users_email_unique UNIQUE (email);

-- Step 5: Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 6: Create trigger for verification_tokens table
DROP TRIGGER IF EXISTS update_verification_tokens_updated_at ON verification_tokens;
CREATE TRIGGER update_verification_tokens_updated_at
    BEFORE UPDATE ON verification_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 7: Add foreign key constraint (optional, for data integrity)
-- ALTER TABLE verification_tokens 
-- ADD CONSTRAINT fk_verification_tokens_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Verification queries to check the migration
-- SELECT COUNT(*) as total_users FROM users;
-- SELECT COUNT(*) as verified_users FROM users WHERE is_verified = TRUE;
-- SELECT COUNT(*) as verification_tokens FROM verification_tokens;

COMMIT;
