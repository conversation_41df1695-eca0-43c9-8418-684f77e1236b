# ⚡ Quick Start: JWT Implementation for SkinAid

## 🎯 **Goal**: Achieve 100% compliance with US-02 Login requirements in 30 minutes

---

## 📋 **Pre-Implementation Checklist**

- [ ] Current authentication system working (register/login basic functionality)
- [ ] Python dependencies installed: `PyJWT`, `python-multipart`
- [ ] Environment variables configured
- [ ] Database connection working

---

## 🚀 **Quick Implementation Steps**

### **Step 1: Install Dependencies (2 minutes)**

```bash
cd backend
pip install PyJWT python-multipart
# or if using poetry
poetry add PyJWT python-multipart
```

### **Step 2: Create JWT Handler (5 minutes)**

**Create file**: `backend/app/core/jwt_handler.py`

```python
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
import os

class JWTHandler:
    def __init__(self):
        self.secret_key = os.getenv("JWT_SECRET_KEY", "dev-secret-key-change-in-production")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 60
    
    def create_access_token(self, user) -> Dict[str, Any]:
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
            "exp": expire,
            "type": "access"
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60,
            "expires_at": expire.isoformat()
        }
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        try:
            return jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
        except:
            return None

# Global instance
jwt_handler = JWTHandler()
```

### **Step 3: Update Login Controller (8 minutes)**

**Edit file**: `backend/app/modules/auth/controllers/auth_controllers.py`

```python
# Add import at top
from app.core.jwt_handler import jwt_handler

# Replace the login_user method return statement:
# OLD CODE (around line 110):
return SuccessResponse(
    message="Đăng nhập thành công",
    data=user_response
)

# NEW CODE:
# Generate JWT token
token_data = jwt_handler.create_access_token(user)

return SuccessResponse(
    message="Đăng nhập thành công",
    data={
        **token_data,  # access_token, token_type, expires_in, expires_at
        "user": user_response,
        "redirect_url": "/dashboard"  # For frontend redirect
    }
)
```

### **Step 4: Update Environment Variables (2 minutes)**

**Edit file**: `backend/.env`

```bash
# Add these lines
JWT_SECRET_KEY=your-super-secret-jwt-key-at-least-32-characters-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

### **Step 5: Test Implementation (5 minutes)**

**Create test file**: `backend/test_jwt_quick.py`

```python
import asyncio
import httpx

async def test_login_jwt():
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        # Register test user
        register_data = {
            "username": "testjwt",
            "email": "<EMAIL>",
            "password": "TestJWT123!"
        }
        
        register_response = await client.post("/api/v1/auth/register", json=register_data)
        print(f"Register: {register_response.status_code}")
        
        # Login and get JWT
        login_data = {
            "email": "<EMAIL>",
            "password": "TestJWT123!"
        }
        
        login_response = await client.post("/api/v1/auth/login", json=login_data)
        print(f"Login: {login_response.status_code}")
        
        if login_response.status_code == 200:
            data = login_response.json()["data"]
            print("✅ JWT Token generated successfully!")
            print(f"Token: {data['access_token'][:50]}...")
            print(f"Expires in: {data['expires_in']} seconds")
            print(f"Redirect URL: {data['redirect_url']}")
        else:
            print("❌ Login failed")
            print(login_response.json())

# Run test
if __name__ == "__main__":
    asyncio.run(test_login_jwt())
```

**Run test**:
```bash
cd backend
python test_jwt_quick.py
```

### **Step 6: Create Protected Route Example (5 minutes)**

**Create file**: `backend/app/core/jwt_middleware.py`

```python
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer
from app.core.jwt_handler import jwt_handler

security = HTTPBearer()

async def get_current_user_id(credentials = Depends(security)):
    """Quick JWT validation - returns user ID"""
    token = credentials.credentials
    payload = jwt_handler.verify_token(token)
    
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    return payload.get("sub")  # User ID
```

**Add protected route** to `backend/app/modules/auth/routes/auth_routers.py`:

```python
from app.core.jwt_middleware import get_current_user_id

@router.get("/profile")
async def get_profile(current_user_id: str = Depends(get_current_user_id)):
    """Protected route example"""
    return {"message": f"Hello user {current_user_id}!", "protected": True}
```

### **Step 7: Frontend Integration Example (3 minutes)**

**JavaScript code for frontend**:

```javascript
// Login function
async function login(email, password) {
    try {
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            // Store token
            localStorage.setItem('access_token', result.data.access_token);
            
            // Redirect to home page (US-02 requirement fulfilled!)
            window.location.href = result.data.redirect_url || '/dashboard';
        } else {
            alert('Login failed: ' + result.message);
        }
    } catch (error) {
        alert('Login error: ' + error.message);
    }
}

// Use token in API calls
function makeAuthenticatedRequest(url) {
    const token = localStorage.getItem('access_token');
    return fetch(url, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
}

// Test protected route
async function testProtectedRoute() {
    const response = await makeAuthenticatedRequest('/api/v1/auth/profile');
    const data = await response.json();
    console.log('Protected route response:', data);
}
```

---

## ✅ **Verification Steps**

1. **Start your FastAPI server**:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   ```

2. **Test login endpoint**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"TestPass123!"}'
   ```

3. **Expected response**:
   ```json
   {
     "success": true,
     "message": "Đăng nhập thành công",
     "data": {
       "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
       "token_type": "bearer",
       "expires_in": 3600,
       "expires_at": "2024-01-01T12:00:00+00:00",
       "user": { ... },
       "redirect_url": "/dashboard"
     }
   }
   ```

4. **Test protected route**:
   ```bash
   curl -X GET "http://localhost:8000/api/v1/auth/profile" \
        -H "Authorization: Bearer YOUR_TOKEN_HERE"
   ```

---

## 🎉 **Success Criteria**

After completing these steps, you should have:

- ✅ **JWT tokens generated on login**
- ✅ **Token-based authentication working**
- ✅ **Protected routes requiring valid tokens**
- ✅ **Frontend redirect capability**
- ✅ **100% US-02 compliance achieved**

---

## 🚨 **Troubleshooting**

**Common Issues**:

1. **"Module not found" error**: Make sure you're in the correct directory and dependencies are installed
2. **"Invalid token" error**: Check JWT_SECRET_KEY in environment variables
3. **"User not found" error**: Make sure you register a user before testing login

**Quick fixes**:
```bash
# Reinstall dependencies
pip install -r requirements.txt

# Check environment variables
echo $JWT_SECRET_KEY

# Restart server
uvicorn app.main:app --reload
```

---

## ⏱️ **Total Time**: ~30 minutes

You now have a complete JWT-based authentication system that meets all US-02 requirements! 🚀
