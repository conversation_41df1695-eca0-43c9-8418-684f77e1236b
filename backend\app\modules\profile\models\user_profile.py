from sqlmodel import SQLModel, Field, Column, Relationship
from sqlalchemy import ForeignKey
import sqlalchemy.dialects.postgresql as pg
import uuid
from typing import Optional, TYPE_CHECKING
from datetime import date
from app.shared.models.basemodel import TimestampMixin
import enum

if TYPE_CHECKING:
    from app.modules.auth.models.user import User


class GenderEnum(str, enum.Enum):
    male = "male"
    female = "female"
    other = "other"


class UserProfile(TimestampMixin, SQLModel, table=True):
    __tablename__ = "user_profiles"

    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )

    user_id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, ForeignKey("users.id"), nullable=False, unique=True, index=True)
    )

    full_name: Optional[str] = Field(default=None, max_length=255, description="Full name of the user")
    phone: Optional[str] = Field(default=None, max_length=20)
    date_of_birth: Optional[date] = Field(default=None)
    gender: Optional[GenderEnum] = Field(default=None)
    address: Optional[str] = Field(default=None, max_length=255)
    avatar_url: Optional[str] = Field(default=None, max_length=500)

    # Quan hệ 1–1 với User
    user: Optional["User"] = Relationship(back_populates="profile")

    def __repr__(self):
        return f"<UserProfile {self.full_name or 'No Name'} - {self.user_id}>"

    @property
    def age(self) -> Optional[int]:
        if not self.date_of_birth:
            return None
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )
