# 🔐 JWT Implementation Guide - Complete Authentication System

## 🎯 **Objective**
Implement JWT token-based authentication to achieve 100% compliance with login requirements (US-02).

---

## 📋 **Implementation Checklist**

- [ ] **Step 1**: Create JWT Handler Service
- [ ] **Step 2**: Update Login Controller to generate tokens
- [ ] **Step 3**: Add JWT middleware for protected routes
- [ ] **Step 4**: Update response schemas
- [ ] **Step 5**: Test implementation

---

## 🔧 **Step 1: Create JWT Handler Service**

### **File**: `backend/app/core/jwt_handler.py`

```python
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
from app.core.config import settings
from app.modules.auth.models.user import User

class JWTHandler:
    """
    JWT Token Handler for SkinAid Authentication
    
    Handles:
    - Access token generation
    - Token validation
    - Token expiration
    - User claims extraction
    """
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
    
    def create_access_token(self, user: User) -> Dict[str, Any]:
        """
        Create JWT access token for authenticated user
        
        Args:
            user: Authenticated user object
            
        Returns:
            Dict containing token and metadata
        """
        # Token expiration time
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=self.access_token_expire_minutes
        )
        
        # JWT payload (claims)
        payload = {
            "sub": str(user.id),  # Subject (user ID)
            "email": user.email,
            "username": user.username,
            "iat": datetime.now(timezone.utc),  # Issued at
            "exp": expire,  # Expiration time
            "type": "access"  # Token type
        }
        
        # Generate JWT token
        token = jwt.encode(
            payload, 
            self.secret_key, 
            algorithm=self.algorithm
        )
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60,  # seconds
            "expires_at": expire.isoformat()
        }
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify and decode JWT token
        
        Args:
            token: JWT token string
            
        Returns:
            Decoded payload if valid, None if invalid
        """
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            return None  # Token expired
        except jwt.InvalidTokenError:
            return None  # Invalid token
    
    def get_user_id_from_token(self, token: str) -> Optional[str]:
        """Extract user ID from valid token"""
        payload = self.verify_token(token)
        if payload:
            return payload.get("sub")
        return None

# Global JWT handler instance
jwt_handler = JWTHandler()
```

---

## ⚙️ **Step 2: Update Configuration**

### **File**: `backend/app/core/config.py`

```python
# Add JWT settings to your config class
class Settings(BaseSettings):
    # ... existing settings ...
    
    # JWT Configuration
    JWT_SECRET_KEY: str = "your-super-secret-jwt-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60  # 1 hour
    
    class Config:
        env_file = ".env"

settings = Settings()
```

---

## 🎮 **Step 3: Update Login Controller**

### **File**: `backend/app/modules/auth/controllers/auth_controllers.py`

```python
# Add import at the top
from app.core.jwt_handler import jwt_handler

# Update login_user method
async def login_user(self, credentials: UserLogin) -> Union[SuccessResponse[Dict], ErrorResponse]:
    """
    Xử lý đăng nhập user với JWT token generation.
    """
    try:
        # Authenticate user (existing logic)
        user = await self.auth_service.authenticate_user(
            credentials.email,
            credentials.password
        )

        # Generate JWT token (NEW)
        token_data = jwt_handler.create_access_token(user)
        
        # Create user response
        user_response = UserResponse(
            user_id=user.id,
            email=user.email,
            username=user.username,
            created_at=user.created_at,
            full_name=user.profile.full_name if user.profile else None,
            phone=user.profile.phone if user.profile else None,
            avatar_url=user.profile.avatar_url if user.profile else None
        )

        # Return complete login response with token
        return SuccessResponse(
            message="Đăng nhập thành công",
            data={
                **token_data,  # access_token, token_type, expires_in, expires_at
                "user": user_response,
                "redirect_url": "/dashboard"  # Frontend redirect URL
            }
        )
        
    except Exception as e:
        # ... existing error handling ...
```

---

## 📝 **Step 4: Update Response Schemas**

### **File**: `backend/app/modules/auth/schemas/auth_responses.py`

```python
from pydantic import BaseModel
from datetime import datetime
from app.modules.auth.schemas.user import UserResponse

class LoginResponse(BaseModel):
    """Complete login response with JWT token"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds
    expires_at: str  # ISO format
    user: UserResponse
    redirect_url: str = "/dashboard"

class TokenData(BaseModel):
    """JWT token payload data"""
    user_id: str
    email: str
    username: str
    exp: datetime
```

---

## 🛡️ **Step 5: Create JWT Middleware**

### **File**: `backend/app/core/jwt_middleware.py`

```python
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.core.jwt_handler import jwt_handler
from app.modules.auth.services.auth_service import AuthService
from app.api.v1.deps import get_db
from sqlmodel.ext.asyncio.session import AsyncSession

# JWT Bearer scheme
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Dependency to get current authenticated user from JWT token
    
    Usage in protected routes:
    @router.get("/protected")
    async def protected_route(current_user: User = Depends(get_current_user)):
        return {"user": current_user.username}
    """
    
    # Extract token
    token = credentials.credentials
    
    # Verify token
    payload = jwt_handler.verify_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user from database
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    auth_service = AuthService(db)
    user = await auth_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account deactivated"
        )
    
    return user
```

---

## 🧪 **Step 6: Testing Implementation**

### **Test Login with JWT**

```python
# File: backend/tests/test_auth_jwt.py
import pytest
from httpx import AsyncClient
from app.core.jwt_handler import jwt_handler

@pytest.mark.asyncio
async def test_login_returns_jwt_token(client: AsyncClient):
    """Test that login returns JWT token"""
    
    # Register user first
    register_data = {
        "username": "testuser",
        "email": "<EMAIL>", 
        "password": "TestPass123!"
    }
    await client.post("/api/v1/auth/register", json=register_data)
    
    # Login
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!"
    }
    
    response = await client.post("/api/v1/auth/login", json=login_data)
    
    assert response.status_code == 200
    data = response.json()["data"]
    
    # Check JWT token fields
    assert "access_token" in data
    assert "token_type" in data
    assert "expires_in" in data
    assert "user" in data
    assert data["token_type"] == "bearer"
    
    # Verify token is valid
    token = data["access_token"]
    payload = jwt_handler.verify_token(token)
    assert payload is not None
    assert payload["email"] == "<EMAIL>"
```

---

## 🚀 **Step 7: Frontend Integration Guide**

### **JavaScript/React Example**

```javascript
// Login function
async function loginUser(email, password) {
    const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
    });
    
    const result = await response.json();
    
    if (response.ok) {
        // Store token
        localStorage.setItem('access_token', result.data.access_token);
        
        // Redirect to home page (US-02 requirement)
        window.location.href = result.data.redirect_url;
    } else {
        // Handle error
        console.error('Login failed:', result.message);
    }
}

// Add token to API requests
function makeAuthenticatedRequest(url, options = {}) {
    const token = localStorage.getItem('access_token');
    
    return fetch(url, {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        }
    });
}
```

---

## ✅ **Verification Checklist**

After implementation, verify:

- [ ] Login returns JWT token in response
- [ ] Token contains correct user information
- [ ] Token expiration works correctly
- [ ] Protected routes require valid token
- [ ] Frontend can store and use token
- [ ] Redirect to home page works
- [ ] Error handling for invalid tokens

---

## 🎉 **Result**

After implementing these changes, your authentication system will be **100% compliant** with both US-01 and US-02 requirements!

**US-02 Login Compliance**:
- ✅ Email and Password validation
- ✅ Email format checking
- ✅ Email existence verification
- ✅ Password matching
- ✅ Error messages for invalid/missing data
- ✅ **Redirect to home page** (via frontend integration)

Ready to implement? Let me know if you need help with any specific step!

---

## 🔒 **Security Best Practices**

### **1. Environment Variables**
```bash
# .env file
JWT_SECRET_KEY=your-super-secure-secret-key-minimum-32-characters
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

### **2. Token Storage (Frontend)**
- ✅ **Recommended**: HttpOnly cookies for web apps
- ⚠️ **Acceptable**: localStorage for SPAs (with XSS protection)
- ❌ **Avoid**: sessionStorage (less secure)

### **3. Rate Limiting**
```python
# Add to login endpoint
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@router.post("/login")
@limiter.limit("5/minute")  # Max 5 login attempts per minute
async def login_user(...):
    # ... existing code
```

### **4. Password Security**
- ✅ Current: Argon2 hashing (excellent choice)
- ✅ Minimum 8 characters with complexity
- ✅ No password in logs or responses

---

## 🚨 **Common Pitfalls to Avoid**

1. **Never log JWT tokens**
2. **Always validate token expiration**
3. **Use HTTPS in production**
4. **Implement proper CORS settings**
5. **Add refresh token mechanism for long sessions**

---

## 📚 **Additional Resources**

- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [FastAPI Security](https://fastapi.tiangolo.com/tutorial/security/)
- [OWASP Authentication Guidelines](https://owasp.org/www-project-cheat-sheets/cheatsheets/Authentication_Cheat_Sheet.html)
