from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional
import uuid
from datetime import datetime

class UserBase(BaseModel):
    email: EmailStr

class UserCreate(UserBase):
    email: EmailStr
    password: str = Field(..., min_length=8, description="Password with at least 8 characters")
    confirm_password: str = Field(..., min_length=8, description="Confirm password must match password")

    @field_validator('confirm_password')
    @classmethod
    def validate_passwords_match(cls, v, info):
        if 'password' in info.data and v != info.data['password']:
            raise ValueError('Passwords do not match')
        return v

class UserResponse(BaseModel):
    user_id: uuid.UUID
    email: EmailStr
    display_name: str
    is_verified: bool
    created_at: datetime

    # Profile information
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class EmailVerificationRequest(BaseModel):
    email: EmailStr
    token: str

class EmailVerificationResponse(BaseModel):
    message: str
    is_verified: bool
