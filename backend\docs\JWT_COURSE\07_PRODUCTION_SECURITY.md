# 🏭 LESSON 07: PRODUCTION SECURITY - BẢO MẬT PRODUCTION

**<PERSON><PERSON><PERSON> tiêu**: Implement enterprise-grade security patterns cho production JWT systems

---

## 🎯 **PHẦN 1: ADVANCED TOKEN PATTERNS - MẪU TOKEN NÂNG CAO**

### **🔄 Token Rotation Strategy**

```python
# auth/token_rotation.py - Advanced token rotation
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import secrets

class TokenRotationManager:
    """Manage token rotation để enhance security"""
    
    def __init__(self):
        self.rotation_threshold = 0.5  # Rotate when 50% of lifetime passed
        self.max_rotation_count = 5    # Max rotations per refresh token
        self.rotation_history = {}     # Track rotation chains
    """
    Giải thích Token Rotation Settings:
    - rotation_threshold: Khi nào trigger rotation (50% lifetime)
    - max_rotation_count: Prevent infinite rotation chains
    - rotation_history: Track token genealogy cho security
    """
    
    def should_rotate_token(self, token_issued_at: datetime, token_expires_at: datetime) -> bool:
        """
        Determine if token should be rotated
        
        Args:
            token_issued_at: When token was created
            token_expires_at: When token expires
        
        Returns:
            bool: True nếu should rotate
        """
        now = datetime.utcnow()
        total_lifetime = token_expires_at - token_issued_at
        elapsed_time = now - token_issued_at
        
        # Calculate percentage of lifetime elapsed
        lifetime_percentage = elapsed_time.total_seconds() / total_lifetime.total_seconds()
        
        return lifetime_percentage >= self.rotation_threshold
        """
        Giải thích Rotation Logic:
        1. Calculate total token lifetime
        2. Calculate elapsed time since issue
        3. Determine percentage of lifetime used
        4. Rotate if >= threshold (50%)
        """
    
    def rotate_refresh_token(self, old_token: str, user_id: int) -> Tuple[str, bool]:
        """
        Rotate refresh token với security checks
        
        Args:
            old_token: Current refresh token
            user_id: User ID
        
        Returns:
            Tuple[new_token, rotation_success]
        """
        # Check rotation history
        rotation_count = self.rotation_history.get(old_token, 0)
        if rotation_count >= self.max_rotation_count:
            # Too many rotations, force re-authentication
            return None, False
        
        # Generate new token
        new_token = secrets.token_urlsafe(32)
        
        # Update rotation history
        self.rotation_history[new_token] = rotation_count + 1
        
        # Revoke old token
        revoke_refresh_token(old_token)
        
        # Create new refresh token record
        expires_delta = timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        create_refresh_token(user_id, expires_delta)
        
        return new_token, True
        """
        Giải thích Rotation Process:
        1. Check rotation count limits
        2. Generate cryptographically secure new token
        3. Track rotation genealogy
        4. Revoke old token immediately
        5. Create new token record
        """
    
    def detect_token_reuse(self, token: str) -> bool:
        """
        Detect if revoked token is being reused (security breach)
        
        Args:
            token: Token being used
        
        Returns:
            bool: True nếu token reuse detected
        """
        # Check if token was previously revoked
        token_data = get_refresh_token(token)
        if token_data and not token_data["is_active"]:
            # Revoked token being reused - security breach!
            self._handle_token_reuse_breach(token_data["user_id"])
            return True
        
        return False
        """
        Giải thích Reuse Detection:
        - Check if token exists but inactive
        - Inactive token usage indicates breach
        - Trigger security incident response
        - Return breach status
        """
    
    def _handle_token_reuse_breach(self, user_id: int):
        """
        Handle token reuse security breach
        
        Args:
            user_id: Affected user ID
        """
        # Revoke all user tokens immediately
        revoke_user_tokens(user_id)
        
        # Log security incident
        security_logger.critical(
            f"Token reuse detected for user {user_id}. All tokens revoked."
        )
        
        # Notify security team
        send_security_alert(f"Token reuse breach - User {user_id}")
        
        # Lock account temporarily
        account_security.lock_account(user_id, duration_hours=24)
        """
        Giải thích Breach Response:
        1. Immediate token revocation
        2. Security logging
        3. Alert security team
        4. Temporary account lock
        5. Force user re-authentication
        """

# Global instance
token_rotation_manager = TokenRotationManager()
```

### **🍪 Secure Cookie Implementation**

```python
# auth/cookie_security.py - Secure cookie management
from fastapi import Response, Request
from typing import Optional

class SecureCookieManager:
    """Manage secure cookies cho refresh tokens"""
    
    def __init__(self):
        self.cookie_name = "refresh_token"
        self.cookie_path = "/auth"
        self.cookie_domain = None  # Set trong production
        self.cookie_max_age = REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
    """
    Giải thích Cookie Settings:
    - cookie_name: Consistent naming
    - cookie_path: Restrict to auth endpoints
    - cookie_domain: Set cho production domain
    - cookie_max_age: Match refresh token lifetime
    """
    
    def set_refresh_token_cookie(self, response: Response, refresh_token: str, secure: bool = True):
        """
        Set secure refresh token cookie
        
        Args:
            response: FastAPI response object
            refresh_token: Refresh token value
            secure: Whether to use Secure flag
        """
        response.set_cookie(
            key=self.cookie_name,
            value=refresh_token,
            max_age=self.cookie_max_age,
            path=self.cookie_path,
            domain=self.cookie_domain,
            secure=secure,           # HTTPS only
            httponly=True,           # No JavaScript access
            samesite="strict"        # CSRF protection
        )
        """
        Giải thích Cookie Security Flags:
        - secure=True: Only send over HTTPS
        - httponly=True: Prevent XSS access
        - samesite="strict": Strongest CSRF protection
        - path="/auth": Limit scope to auth endpoints
        """
    
    def get_refresh_token_from_cookie(self, request: Request) -> Optional[str]:
        """
        Extract refresh token từ cookie
        
        Args:
            request: FastAPI request object
        
        Returns:
            Optional[str]: Refresh token hoặc None
        """
        return request.cookies.get(self.cookie_name)
        """
        Giải thích Cookie Extraction:
        - Simple cookie access
        - Return None if not present
        - Used trong refresh endpoints
        """
    
    def clear_refresh_token_cookie(self, response: Response):
        """
        Clear refresh token cookie (logout)
        
        Args:
            response: FastAPI response object
        """
        response.set_cookie(
            key=self.cookie_name,
            value="",
            max_age=0,
            path=self.cookie_path,
            domain=self.cookie_domain,
            secure=True,
            httponly=True,
            samesite="strict"
        )
        """
        Giải thích Cookie Clearing:
        - Set empty value
        - Set max_age=0 để immediate expiry
        - Same security flags để ensure clearing
        """

# Global instance
cookie_manager = SecureCookieManager()
```

## 🔐 **PHẦN 2: MULTI-FACTOR AUTHENTICATION**

### **📱 MFA Implementation**

```python
# auth/mfa.py - Multi-factor authentication
import pyotp
import qrcode
from io import BytesIO
import base64
from typing import Optional, Tuple

class MFAManager:
    """Multi-factor authentication management"""
    
    def __init__(self):
        self.issuer_name = "SkinAid App"
        self.backup_codes_count = 10
    """
    Giải thích MFA Settings:
    - issuer_name: App name trong authenticator
    - backup_codes_count: Number of backup codes
    - TOTP-based MFA (Time-based One-Time Password)
    """
    
    def generate_mfa_secret(self, user_email: str) -> Tuple[str, str]:
        """
        Generate MFA secret và QR code
        
        Args:
            user_email: User email address
        
        Returns:
            Tuple[secret_key, qr_code_base64]
        """
        # Generate secret key
        secret = pyotp.random_base32()
        """
        Giải thích Secret Generation:
        - pyotp.random_base32(): Cryptographically secure
        - Base32 encoding cho compatibility
        - 160-bit entropy (32 chars * 5 bits)
        """
        
        # Create TOTP URI
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer_name
        )
        """
        Giải thích TOTP URI:
        - Standard format cho authenticator apps
        - Contains secret, issuer, account name
        - Compatible với Google Authenticator, Authy, etc.
        """
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # Convert to base64 image
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return secret, qr_code_base64
        """
        Giải thích QR Code Generation:
        1. Create QR code với TOTP URI
        2. Generate PNG image
        3. Convert to base64 cho web display
        4. Return both secret và QR code
        """
    
    def verify_mfa_token(self, secret: str, token: str) -> bool:
        """
        Verify MFA token
        
        Args:
            secret: User's MFA secret
            token: 6-digit token từ authenticator
        
        Returns:
            bool: True nếu token valid
        """
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)
        """
        Giải thích Token Verification:
        - TOTP generates 6-digit codes every 30 seconds
        - valid_window=1: Accept previous/current/next window
        - Accounts cho clock skew
        - Return boolean verification result
        """
    
    def generate_backup_codes(self) -> list[str]:
        """
        Generate backup codes cho MFA recovery
        
        Returns:
            List of backup codes
        """
        backup_codes = []
        for _ in range(self.backup_codes_count):
            code = secrets.token_hex(4).upper()  # 8-char hex codes
            backup_codes.append(code)
        
        return backup_codes
        """
        Giải thích Backup Codes:
        - Single-use codes cho MFA recovery
        - 8-character hex strings
        - Store hashed trong database
        - Allow recovery nếu lose authenticator
        """
    
    def verify_backup_code(self, user_id: int, code: str) -> bool:
        """
        Verify và consume backup code
        
        Args:
            user_id: User ID
            code: Backup code to verify
        
        Returns:
            bool: True nếu code valid và consumed
        """
        # Get user's backup codes từ database
        user_backup_codes = get_user_backup_codes(user_id)
        
        # Hash provided code
        code_hash = hashlib.sha256(code.encode()).hexdigest()
        
        # Check if code exists và unused
        if code_hash in user_backup_codes and user_backup_codes[code_hash]["used"] == False:
            # Mark code as used
            mark_backup_code_used(user_id, code_hash)
            return True
        
        return False
        """
        Giải thích Backup Code Verification:
        1. Hash provided code
        2. Check against stored hashes
        3. Verify code not already used
        4. Mark as used if valid
        5. Single-use security
        """

# Global instance
mfa_manager = MFAManager()
```

### **🔐 MFA-Enhanced Authentication**

```python
# auth/routes.py - MFA-enhanced endpoints
@router.post("/login-mfa", response_model=Dict)
async def login_with_mfa(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    Login với MFA support
    """
    # Standard authentication first
    user_dict = authenticate_user(form_data.username, form_data.password)
    if not user_dict:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    
    # Check if MFA enabled
    if user_dict.get("mfa_enabled", False):
        # Return MFA challenge
        return {
            "mfa_required": True,
            "message": "Please provide MFA token",
            "temp_token": create_temp_mfa_token(user_dict["id"])
        }
    else:
        # Standard login flow
        return create_token_pair(user_dict)
    """
    Giải thích MFA Login Flow:
    1. Verify username/password first
    2. Check if user has MFA enabled
    3. Return MFA challenge if enabled
    4. Provide temporary token cho MFA verification
    5. Standard flow if no MFA
    """

@router.post("/verify-mfa", response_model=TokenPair)
async def verify_mfa_and_login(
    temp_token: str,
    mfa_token: str
):
    """
    Verify MFA token và complete login
    """
    # Verify temporary token
    temp_payload = verify_temp_mfa_token(temp_token)
    if not temp_payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired MFA session"
        )
    
    user_id = temp_payload["user_id"]
    user_dict = get_user_by_id(user_id)
    
    # Verify MFA token
    if not mfa_manager.verify_mfa_token(user_dict["mfa_secret"], mfa_token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid MFA token"
        )
    
    # MFA successful, create full token pair
    return create_token_pair(user_dict)
    """
    Giải thích MFA Verification:
    1. Verify temporary MFA token
    2. Extract user information
    3. Verify 6-digit MFA code
    4. Create full authentication tokens
    5. Complete login process
    """

@router.post("/setup-mfa")
async def setup_mfa(
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Setup MFA cho user
    """
    # Generate MFA secret và QR code
    secret, qr_code = mfa_manager.generate_mfa_secret(current_user.email)
    
    # Generate backup codes
    backup_codes = mfa_manager.generate_backup_codes()
    
    # Store secret trong database (encrypted)
    store_user_mfa_secret(current_user.id, secret)
    store_user_backup_codes(current_user.id, backup_codes)
    
    return {
        "secret": secret,
        "qr_code": qr_code,
        "backup_codes": backup_codes,
        "message": "Scan QR code với authenticator app"
    }
    """
    Giải thích MFA Setup:
    1. Generate secret và QR code
    2. Generate backup codes
    3. Store encrypted trong database
    4. Return setup information
    5. User scans QR code
    """
```

## 🎯 **LESSON 07 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Production Security Features:**
- **Token Rotation**: Automatic refresh token rotation với breach detection
- **Secure Cookies**: httpOnly, Secure, SameSite cookie implementation
- **Multi-Factor Authentication**: TOTP-based MFA với backup codes
- **Security Incident Response**: Automated breach detection và response
- **Advanced Token Management**: Rotation limits, genealogy tracking

### **🧠 Critical Production Understanding:**
- **Token Rotation Benefits**: Limits breach exposure window
- **Cookie Security**: XSS và CSRF protection strategies
- **MFA Implementation**: Balance security với user experience
- **Incident Response**: Automated security breach handling
- **Defense in Depth**: Multiple security layers working together

### **🔧 Production Deployment Considerations:**
- **Secret Management**: Use HSM hoặc key management services
- **Monitoring**: Comprehensive security event tracking
- **Alerting**: Real-time security incident notifications
- **Performance**: Optimize security checks cho scale
- **Compliance**: Meet regulatory requirements

### **🎯 Next Steps:**
- **Lesson 08**: Performance & Scaling
- **Practice**: Test security measures under load
- **Monitoring**: Implement security dashboards

**Bạn đã có enterprise-grade JWT security! Ready for performance optimization?** 🚀
