from pydantic_settings import BaseSettings, SettingsConfigDict 
from typing import List
class Setting(BaseSettings): 

    APP_NAME: str = "Wound Detection API"
    VERSION: str = "0.1.0"
    DEBUG: bool = True

    API_V1_STR: str ="/api/v1"

    DATABASE_URL: str 

    MAX_UPLOAD_SIZE: int = 5 * 1024 * 1024
    ALLOWED_EXTENSIONS: str = ".jpg,.jpeg,.png"
    UPLOAD_DIR: str = "./uploads"

    model_config = SettingsConfigDict(
        env_file=".env", 
        env_file_encoding="utf-8",
        extra="ignore"
    )

    @property
    def ALLOWED_EXTENSIONS_LIST(self) -> List[str]:
        return self.ALLOWED_EXTENSIONS.split(",")

settings = Setting()