from sqlmodel import SQLModel, Field, Column, Relationship
import uuid
import sqlalchemy.dialects.postgresql as pg
from typing import Optional, List, TYPE_CHECKING
from app.shared.models.basemodel import TimestampMixin

if TYPE_CHECKING:
    from app.modules.profile.models.user_profile import UserProfile
    from app.modules.auth.models.verification_token import VerificationToken


class User(TimestampMixin, SQLModel, table=True):
    __tablename__ = "users"

    id: uuid.UUID = Field(
        sa_column=Column(pg.UUID, primary_key=True, default=uuid.uuid4)
    )

    email: str = Field(unique=True, nullable=False, index=True)
    hashed_password: str = Field(nullable=False)

    is_active: bool = Field(default=True, nullable=False)
    is_verified: bool = Field(default=False, nullable=False)

    # 1–1 với UserProfile
    profile: Optional["UserProfile"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={
            "uselist": False,
            "cascade": "all, delete-orphan"
        }
    )

    # 1–N với VerificationToken
    verification_tokens: List["VerificationToken"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    def __repr__(self):
        return f"<User {self.email}>"

    @property
    def display_name(self) -> str:
        return self.email.split('@')[0]
