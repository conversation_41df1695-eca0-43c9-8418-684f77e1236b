# 🔄 LESSON 04: REFRESH TOKEN STRATEGY - CHIẾN LƯỢC TOKEN LÂU DÀI

**Mục tiêu**: Implement dual token system với refresh tokens để balance security và user experience

---

## 🎯 **PHẦN 1: DUAL TOKEN PROBLEM - VẤN ĐỀ HAI TOKEN**

### **🤔 Tại sao cần Refresh Token?**

```python
# Problem với single access token:
"""
SCENARIO 1: Long-lived Access Token (24 hours)
✅ UX: User không cần login lại thường xuyên
❌ Security: Nếu token bị steal → attacker có 24h access
❌ Control: Kh<PERSON><PERSON> thể revoke token trước expiry

SCENARIO 2: Short-lived Access Token (15 minutes)  
✅ Security: Limited damage nếu token stolen
❌ UX: User phải login lại mỗi 15 phút
❌ Mobile: App constantly asking for credentials
"""

# Solution: Dual Token Strategy
"""
ACCESS TOKEN:
- Short-lived (15-30 minutes)
- Used for API requests
- Contains minimal user info
- Stateless, không cần database lookup

REFRESH TOKEN:
- Long-lived (7-30 days)
- Used to get new access tokens
- Stored in database (can be revoked)
- More secure storage (httpOnly cookie)
"""
```

**Giải thích Dual Token Benefits:**
- **Security**: Short access token limits exposure window
- **UX**: Long refresh token prevents frequent re-authentication
- **Control**: Can revoke refresh tokens immediately
- **Flexibility**: Different storage strategies cho different token types

### **🔄 Refresh Token Workflow**

```python
# Complete dual token flow:
"""
1. INITIAL LOGIN:
   User → Server: {email, password}
   Server → User: {access_token, refresh_token}
   
2. API REQUESTS:
   User → Server: Authorization: Bearer <access_token>
   Server: Verify access_token → Process request
   
3. ACCESS TOKEN EXPIRES:
   User → Server: Authorization: Bearer <expired_access_token>
   Server → User: 401 Unauthorized
   
4. TOKEN REFRESH:
   User → Server: {refresh_token}
   Server: Verify refresh_token → Generate new access_token
   Server → User: {new_access_token}
   
5. CONTINUE API REQUESTS:
   User → Server: Authorization: Bearer <new_access_token>
"""
```

**Giải thích Workflow Steps:**
- **Step 1**: Traditional login nhưng return both tokens
- **Step 2**: Normal API usage với access token
- **Step 3**: Client detect 401 và trigger refresh
- **Step 4**: Exchange refresh token cho new access token
- **Step 5**: Resume normal operations

## 🗄️ **PHẦN 2: DATABASE MODELS - MÔ HÌNH DỮ LIỆU**

### **🔑 Refresh Token Model**

```python
# auth/models.py - Add refresh token models
from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class RefreshTokenCreate(BaseModel):
    """Model cho refresh token creation"""
    user_id: int
    expires_at: datetime
    device_info: Optional[str] = None
    """
    Giải thích fields:
    - user_id: Link token với specific user
    - expires_at: When token becomes invalid
    - device_info: Track which device token belongs to
    """

class RefreshTokenInDB(BaseModel):
    """Model cho refresh token trong database"""
    id: int
    token: str
    user_id: int
    expires_at: datetime
    created_at: datetime
    is_active: bool
    device_info: Optional[str] = None
    last_used_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
    """
    Giải thích additional fields:
    - id: Primary key cho database
    - token: Actual refresh token string
    - created_at: Track when token was issued
    - is_active: Allow manual revocation
    - last_used_at: Track usage patterns
    """

class TokenPair(BaseModel):
    """Model cho token pair response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    """
    Giải thích TokenPair:
    - access_token: Short-lived JWT
    - refresh_token: Long-lived random string
    - token_type: Always "bearer" for JWT
    - expires_in: Access token lifetime in seconds
    """

class RefreshRequest(BaseModel):
    """Model cho refresh token request"""
    refresh_token: str
    """
    Giải thích RefreshRequest:
    - Simple model với chỉ refresh_token field
    - Client gửi refresh token để get new access token
    - Validation ensures token format correct
    """
```

### **🗃️ Database Schema**

```python
# database/refresh_tokens.py - Fake database extension
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import secrets

# Fake refresh tokens storage
fake_refresh_tokens: Dict[str, Dict] = {}
"""
Giải thích fake storage:
- Key: refresh_token string
- Value: token metadata dict
- Production sẽ dùng real database table
"""

def create_refresh_token(user_id: int, expires_delta: timedelta, device_info: Optional[str] = None) -> str:
    """
    Tạo refresh token mới
    
    Args:
        user_id: ID của user
        expires_delta: Token lifetime
        device_info: Optional device information
    
    Returns:
        str: Refresh token string
    """
    # Generate cryptographically secure random token
    token = secrets.token_urlsafe(32)
    """
    Giải thích token generation:
    - secrets.token_urlsafe(): Cryptographically secure random
    - 32 bytes = 256 bits entropy
    - URL-safe base64 encoding
    - Unpredictable và unique
    """
    
    # Calculate expiration time
    expires_at = datetime.utcnow() + expires_delta
    
    # Store token metadata
    fake_refresh_tokens[token] = {
        "id": len(fake_refresh_tokens) + 1,
        "token": token,
        "user_id": user_id,
        "expires_at": expires_at,
        "created_at": datetime.utcnow(),
        "is_active": True,
        "device_info": device_info,
        "last_used_at": None
    }
    """
    Giải thích token storage:
    - Store all metadata cần thiết cho validation
    - expires_at: Check token expiration
    - is_active: Allow manual revocation
    - created_at, last_used_at: Audit trail
    """
    
    return token

def get_refresh_token(token: str) -> Optional[Dict]:
    """
    Get refresh token metadata
    
    Args:
        token: Refresh token string
    
    Returns:
        Dict với token metadata hoặc None
    """
    token_data = fake_refresh_tokens.get(token)
    if not token_data:
        return None
    
    # Check if token expired
    if token_data["expires_at"] < datetime.utcnow():
        return None
    
    # Check if token active
    if not token_data["is_active"]:
        return None
    
    return token_data
    """
    Giải thích validation logic:
    1. Check token exists trong storage
    2. Check token chưa expired
    3. Check token chưa bị revoked
    4. Return metadata nếu all checks pass
    """

def revoke_refresh_token(token: str) -> bool:
    """
    Revoke refresh token (mark as inactive)
    
    Args:
        token: Token to revoke
    
    Returns:
        bool: True nếu revoked successfully
    """
    if token in fake_refresh_tokens:
        fake_refresh_tokens[token]["is_active"] = False
        return True
    return False
    """
    Giải thích revocation:
    - Set is_active = False thay vì delete
    - Maintain audit trail
    - Prevent token reuse
    - Return success status
    """

def revoke_user_tokens(user_id: int) -> int:
    """
    Revoke tất cả refresh tokens của user
    
    Args:
        user_id: User ID
    
    Returns:
        int: Number of tokens revoked
    """
    revoked_count = 0
    for token_data in fake_refresh_tokens.values():
        if token_data["user_id"] == user_id and token_data["is_active"]:
            token_data["is_active"] = False
            revoked_count += 1
    
    return revoked_count
    """
    Giải thích bulk revocation:
    - Useful cho logout all devices
    - Security incident response
    - Account deactivation
    - Return count cho confirmation
    """
```

## 🔧 **PHẦN 3: ENHANCED SECURITY SERVICE**

### **🛡️ Enhanced JWT Service**

```python
# auth/security.py - Enhanced với refresh token support
from datetime import timedelta
from database.refresh_tokens import create_refresh_token, get_refresh_token, revoke_refresh_token

# Add refresh token settings
REFRESH_TOKEN_EXPIRE_DAYS = 7

def create_token_pair(user_data: Dict, device_info: Optional[str] = None) -> Dict:
    """
    Tạo access token và refresh token pair
    
    Args:
        user_data: User information để include trong access token
        device_info: Optional device information
    
    Returns:
        Dict với both tokens và metadata
    """
    # Create access token (same as before)
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user_data["username"]},
        expires_delta=access_token_expires
    )
    """
    Giải thích access token creation:
    - Same logic như single token approach
    - Short-lived (15-30 minutes)
    - Contains minimal user info
    """
    
    # Create refresh token
    refresh_token_expires = timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    refresh_token = create_refresh_token(
        user_id=user_data["id"],
        expires_delta=refresh_token_expires,
        device_info=device_info
    )
    """
    Giải thích refresh token creation:
    - Long-lived (7 days default)
    - Random string, không phải JWT
    - Stored trong database với metadata
    """
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "refresh_expires_in": REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
    }
    """
    Giải thích response format:
    - access_token: JWT for API requests
    - refresh_token: Random string for token refresh
    - expires_in: Access token lifetime (seconds)
    - refresh_expires_in: Refresh token lifetime (seconds)
    """

def refresh_access_token(refresh_token: str) -> Optional[Dict]:
    """
    Generate new access token using refresh token
    
    Args:
        refresh_token: Valid refresh token
    
    Returns:
        Dict với new access token hoặc None nếu invalid
    """
    # Validate refresh token
    token_data = get_refresh_token(refresh_token)
    if not token_data:
        return None
    """
    Giải thích validation:
    - get_refresh_token() checks existence, expiration, active status
    - Return None nếu any validation fails
    - Secure by default approach
    """
    
    # Update last used timestamp
    token_data["last_used_at"] = datetime.utcnow()
    
    # Get user data
    from database.fake_db import get_user_by_id
    user_data = get_user_by_id(token_data["user_id"])
    if not user_data:
        # User deleted, revoke token
        revoke_refresh_token(refresh_token)
        return None
    """
    Giải thích user validation:
    - Check user still exists
    - User có thể bị deleted sau khi token issued
    - Revoke token nếu user không tồn tại
    """
    
    # Create new access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token = create_access_token(
        data={"sub": user_data["username"]},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": new_access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }
    """
    Giải thích new token creation:
    - Generate fresh access token với current user data
    - Same expiration time như original
    - Don't return new refresh token (reuse existing)
    """
```

## 🎯 **LESSON 04 SUMMARY - TÓM TẮT BÀI HỌC**

### **✅ Key Concepts Implemented:**
- **Dual Token Strategy**: Access + Refresh token combination
- **Database Storage**: Refresh token persistence và metadata
- **Token Validation**: Expiration, revocation, user existence checks
- **Security Enhancement**: Cryptographically secure token generation
- **Audit Trail**: Creation, usage, revocation tracking

### **🧠 Critical Understanding:**
- **Security vs UX Balance**: Short access tokens với long refresh tokens
- **Stateful Refresh Tokens**: Database storage enables revocation
- **Token Lifecycle**: Creation, usage, refresh, revocation
- **Device Tracking**: Optional device information cho security

### **🔧 Production Considerations:**
- **Database Design**: Proper indexing cho performance
- **Token Rotation**: Consider rotating refresh tokens
- **Rate Limiting**: Prevent refresh token abuse
- **Monitoring**: Track token usage patterns

### **🎯 Next Steps:**
- **Lesson 05**: Role-Based Access Control
- **Practice**: Implement token refresh flow
- **Security**: Add token rotation strategy

**Bạn đã có dual token system! Ready for RBAC?** 🚀
