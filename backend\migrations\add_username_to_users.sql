
-- Add username column to users table
ALTER TABLE users
ADD COLUMN username VA<PERSON>HAR(50) UNIQUE;

-- Create index on username for better performance
CREATE INDEX idx_users_username ON users(username);

-- Update existing users to have username based on email (temporary)
UPDATE users
SET username = <PERSON>LIT_PART(email, '@', 1)
WHERE username IS NULL;

-- Make username NOT NULL after updating existing records
ALTER TABLE users
ALTER COLUMN username SET NOT NULL;

-- Add full_name column to user_profiles table
ALTER TABLE user_profiles
ADD COLUMN full_name VARCHAR(255);

-- Create index on full_name for better search performance
CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);

