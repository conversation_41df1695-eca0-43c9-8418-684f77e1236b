# 🔧 SERVICES DESIGN - HƯỚNG DẪN CHI TIẾT

**<PERSON><PERSON><PERSON> tiêu**: Hướng dẫn cách suy luận và thiết kế service layer từ business requirements

---

## 🎯 **QUY TRÌNH THIẾT KẾ SERVICES**

### **Bước 1: Business Operations Analysis - Phân tích hoạt động nghiệp vụ**

#### **🤔 Câu hỏi cốt lõi:**
```
1. Domain này có những operations gì?
2. Operations nào liên quan đến nhau?
3. Business rules gì cần enforce?
4. External dependencies nào cần tương tác?
5. Data transformations nào cần thiết?
```

#### **Ví dụ thực tế: Authentication Domain**
```python
# Business Operations Analysis:
"""
Authentication Domain Operations:
1. User Registration
   - Validate input data
   - Check uniqueness constraints
   - Hash password
   - Create user record
   - Create initial profile
   - Send welcome email

2. User Authentication
   - Validate credentials
   - Check account status
   - Generate session/token
   - Update last login

3. Password Management
   - Change password (authenticated)
   - Reset password (forgot password)
   - Validate password strength

4. Account Management
   - Activate/deactivate account
   - Verify email
   - Update user information
"""

# Service Identification:
services = {
    "AuthService": "Core authentication operations",
    "UserService": "User data management", 
    "EmailService": "Email notifications",
    "TokenService": "JWT token management"
}
```

### **Bước 2: Service Boundaries Design - Thiết kế ranh giới service**

#### **🧠 Suy luận service responsibilities:**
```python
# Single Responsibility Principle Application
class AuthService:
    """
    Suy luận: AuthService chỉ lo authentication logic
    
    ✅ Responsibilities:
    - User registration
    - User authentication  
    - Password validation
    - Account status checks
    
    ❌ NOT responsibilities:
    - Email sending (→ EmailService)
    - Token generation (→ TokenService)
    - File uploads (→ FileService)
    - Business analytics (→ AnalyticsService)
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # Inject other services as dependencies
        self.email_service = EmailService()
        self.token_service = TokenService()

class UserService:
    """
    Suy luận: UserService lo user data management
    
    ✅ Responsibilities:
    - User CRUD operations
    - Profile management
    - User search and filtering
    - User data validation
    
    ❌ NOT responsibilities:
    - Authentication (→ AuthService)
    - Password hashing (→ AuthService)
    - Email sending (→ EmailService)
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db

# Service Interaction Pattern
class AuthController:
    """Controller orchestrates multiple services"""
    
    def __init__(self, db: AsyncSession):
        self.auth_service = AuthService(db)
        self.user_service = UserService(db)
        self.email_service = EmailService()
    
    async def register(self, user_data: UserCreate):
        # Orchestrate multiple services
        user = await self.auth_service.create_user(user_data)
        profile = await self.user_service.create_profile(user.id)
        await self.email_service.send_welcome_email(user.email)
        return user
```

### **Bước 3: Method Design - Thiết kế phương thức**

#### **🤔 Suy luận method signatures:**
```python
class AuthService:
    """
    Suy luận method design principles:
    1. Clear, descriptive names
    2. Single responsibility per method
    3. Appropriate input/output types
    4. Consistent error handling
    5. Async for I/O operations
    """
    
    async def create_user(self, user_data: UserCreate) -> User:
        """
        Suy luận signature:
        - Input: UserCreate (validated schema)
        - Output: User (domain model)
        - Async: Database I/O operation
        - Throws: Business exceptions
        """
        
    async def authenticate_user(self, email: str, password: str) -> User:
        """
        Suy luận signature:
        - Input: Primitives (simple, clear intent)
        - Output: User (authenticated user data)
        - Throws: AuthException if invalid
        """
        
    async def change_password(self, user_id: uuid.UUID, old_password: str, new_password: str) -> bool:
        """
        Suy luận signature:
        - Input: ID + passwords (clear parameters)
        - Output: bool (success indicator)
        - Throws: AuthException if validation fails
        """
        
    def validate_password_strength(self, password: str) -> ValidationResult:
        """
        Suy luận signature:
        - Input: str (password to validate)
        - Output: ValidationResult (detailed feedback)
        - Sync: No I/O, pure validation logic
        """

# Supporting types
class ValidationResult:
    """Structured validation feedback"""
    is_valid: bool
    errors: List[str]
    suggestions: List[str]
```

### **Bước 4: Business Logic Implementation - Triển khai logic nghiệp vụ**

#### **🧠 Suy luận business flow:**
```python
class AuthService:
    async def create_user(self, user_data: UserCreate) -> User:
        """
        Suy luận business flow:
        1. Input validation (business rules)
        2. Uniqueness checks (business constraints)
        3. Data transformation (security requirements)
        4. Database operations (persistence)
        5. Related entity creation (data consistency)
        """
        
        # Step 1: Business validation
        validation_result = self.validate_user_data(user_data)
        if not validation_result.is_valid:
            raise ValidationException(
                message="User data validation failed",
                error_code=ErrorCodes.VAL_INVALID_DATA,
                context={"errors": validation_result.errors}
            )
        
        # Step 2: Business constraints
        if await self.email_exists(user_data.email):
            raise EmailAlreadyExistsError(user_data.email)
        
        if await self.username_exists(user_data.username):
            raise UsernameAlreadyExistsError(user_data.username)
        
        # Step 3: Data transformation
        hashed_password = self.hash_password(user_data.password)
        
        # Step 4: Database transaction
        async with self.db.begin():
            # Create user
            user = User(
                username=user_data.username,
                email=user_data.email,
                hashed_password=hashed_password,
                is_active=True,
                is_verified=False
            )
            
            self.db.add(user)
            await self.db.flush()  # Get user.id without committing
            
            # Create related entities
            profile = UserProfile(
                user_id=user.id,
                full_name=None  # Will be updated later
            )
            
            self.db.add(profile)
            # Transaction commits automatically
        
        # Step 5: Post-creation operations
        await self.db.refresh(user)
        return user
    
    def validate_user_data(self, user_data: UserCreate) -> ValidationResult:
        """
        Suy luận validation logic:
        - Separate method for testability
        - Business rules, not just format validation
        - Detailed feedback for user experience
        """
        errors = []
        suggestions = []
        
        # Username validation
        if len(user_data.username) < 2:
            errors.append("Username must be at least 2 characters")
            suggestions.append("Try a longer username")
        
        if not re.match(r'^[a-zA-Z0-9_-]+$', user_data.username):
            errors.append("Username can only contain letters, numbers, underscore, and hyphen")
            suggestions.append("Remove special characters from username")
        
        # Password validation
        password_result = self.validate_password_strength(user_data.password)
        if not password_result.is_valid:
            errors.extend(password_result.errors)
            suggestions.extend(password_result.suggestions)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            suggestions=suggestions
        )
```

### **Bước 5: Data Access Patterns - Mẫu truy cập dữ liệu**

#### **🤔 Suy luận database interaction patterns:**
```python
class AuthService:
    """
    Suy luận data access strategy:
    1. Repository pattern vs direct ORM
    2. Query optimization
    3. Transaction management
    4. Error handling
    """
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Suy luận: Simple lookup operation
        - Use ORM for simple queries
        - Add proper indexing
        - Handle case sensitivity
        """
        try:
            result = await self.db.execute(
                select(User).where(User.email.ilike(email))  # Case-insensitive
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Database error in get_user_by_email: {str(e)}")
            raise DatabaseConnectionError(
                connection_string=str(self.db.bind.url),
                original_error=str(e)
            )
    
    async def get_user_with_profile(self, user_id: uuid.UUID) -> Optional[User]:
        """
        Suy luận: Complex query with relationships
        - Use JOIN to avoid N+1 queries
        - Load related data in single query
        - Handle missing relationships gracefully
        """
        try:
            result = await self.db.execute(
                select(User)
                .options(selectinload(User.profile))  # Eager load profile
                .where(User.id == user_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Database error in get_user_with_profile: {str(e)}")
            raise DatabaseConnectionError(original_error=str(e))
    
    async def search_users(self, query: str, limit: int = 10) -> List[User]:
        """
        Suy luận: Search operation
        - Use full-text search for better performance
        - Implement pagination
        - Consider search ranking
        """
        try:
            # PostgreSQL full-text search
            search_vector = func.to_tsvector('english', 
                func.concat(User.username, ' ', User.email)
            )
            search_query = func.plainto_tsquery('english', query)
            
            result = await self.db.execute(
                select(User)
                .where(search_vector.match(search_query))
                .order_by(func.ts_rank(search_vector, search_query).desc())
                .limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            # Fallback to simple LIKE search
            return await self._simple_user_search(query, limit)
```

### **Bước 6: External Service Integration - Tích hợp dịch vụ ngoài**

#### **🧠 Suy luận external dependencies:**
```python
class AuthService:
    """
    Suy luận external service integration:
    1. Dependency injection for testability
    2. Circuit breaker for resilience
    3. Retry logic for transient failures
    4. Fallback strategies
    """
    
    def __init__(self, db: AsyncSession, email_service: EmailService = None):
        self.db = db
        self.email_service = email_service or EmailService()
    
    async def create_user(self, user_data: UserCreate) -> User:
        # ... user creation logic ...
        
        # External service call with error handling
        try:
            await self.email_service.send_welcome_email(
                email=user.email,
                username=user.username
            )
        except EmailServiceException as e:
            # Log error but don't fail user creation
            logger.warning(f"Failed to send welcome email: {str(e)}")
            # Could queue for retry later
            await self.queue_welcome_email(user.id)
        
        return user

class EmailService:
    """
    Suy luận: External service wrapper
    - Abstract external API details
    - Implement retry logic
    - Handle rate limiting
    - Provide fallback options
    """
    
    def __init__(self, smtp_config: SMTPConfig):
        self.smtp_config = smtp_config
        self.retry_count = 3
        self.retry_delay = 1.0
    
    async def send_welcome_email(self, email: str, username: str) -> bool:
        """
        Suy luận: Resilient external service call
        - Retry transient failures
        - Circuit breaker pattern
        - Structured logging
        """
        for attempt in range(self.retry_count):
            try:
                await self._send_email(
                    to=email,
                    subject="Welcome to SkinAid!",
                    template="welcome",
                    context={"username": username}
                )
                return True
                
            except TransientEmailError as e:
                if attempt < self.retry_count - 1:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
                else:
                    raise EmailServiceException(
                        message="Failed to send email after retries",
                        error_code=ErrorCodes.SYS_EXTERNAL_SERVICE,
                        context={"email": email, "attempts": self.retry_count}
                    )
            
            except PermanentEmailError as e:
                # Don't retry permanent failures
                raise EmailServiceException(
                    message="Permanent email service error",
                    error_code=ErrorCodes.SYS_EXTERNAL_SERVICE,
                    context={"email": email, "error": str(e)}
                )
```

### **Bước 7: Testing Strategy - Chiến lược kiểm thử**

#### **🤔 Suy luận testing approach:**
```python
# Unit testing services
class TestAuthService:
    """
    Suy luận testing strategy:
    1. Mock external dependencies
    2. Test business logic in isolation
    3. Test error scenarios
    4. Test edge cases
    """
    
    @pytest.fixture
    def auth_service(self, mock_db, mock_email_service):
        return AuthService(db=mock_db, email_service=mock_email_service)
    
    async def test_create_user_success(self, auth_service):
        """Test happy path"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        user = await auth_service.create_user(user_data)
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.is_active is True
        assert user.is_verified is False
    
    async def test_create_user_duplicate_email(self, auth_service, mock_db):
        """Test business rule violation"""
        # Setup: existing user
        mock_db.execute.return_value.scalar_one_or_none.return_value = User(email="<EMAIL>")
        
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        with pytest.raises(EmailAlreadyExistsError):
            await auth_service.create_user(user_data)
    
    async def test_create_user_email_service_failure(self, auth_service, mock_email_service):
        """Test external service failure handling"""
        mock_email_service.send_welcome_email.side_effect = EmailServiceException("SMTP error")
        
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>", 
            password="SecurePass123!"
        )
        
        # Should still create user even if email fails
        user = await auth_service.create_user(user_data)
        assert user is not None
```

---

## 🎯 **BEST PRACTICES SUMMARY**

### **✅ DO:**
- Define clear service boundaries and responsibilities
- Use dependency injection for external services
- Implement comprehensive error handling
- Use transactions for data consistency
- Add retry logic for external service calls
- Write unit tests for business logic
- Log important operations and errors
- Use async/await for I/O operations

### **❌ DON'T:**
- Mix presentation logic in services
- Create god services with too many responsibilities
- Ignore transaction boundaries
- Hardcode external service configurations
- Skip error handling for external calls
- Write services that are hard to test
- Expose database implementation details

### **🔍 REVIEW CHECKLIST:**
- [ ] Service responsibilities clearly defined?
- [ ] Business logic properly encapsulated?
- [ ] Error handling comprehensive?
- [ ] External dependencies properly abstracted?
- [ ] Database operations optimized?
- [ ] Unit tests cover business scenarios?
- [ ] Logging provides adequate debugging info?

**Remember: Well-designed services are the backbone of maintainable applications!** 🔧
